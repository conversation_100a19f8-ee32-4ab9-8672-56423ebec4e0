{"name": "hehe-miner", "version": "0.1.0", "private": true, "repository": {"type": "git", "url": "https://github.com/tahafarooqui664/hehe-miner.git"}, "author": "tfarooqui664", "scripts": {"dev": "next dev", "build": "prisma generate --no-engine && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "db:push": "prisma db push", "db:seed": "prisma db seed", "vercel-build": "prisma generate --no-engine && prisma db push && next build"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.9.0", "@ton/core": "^0.56.3", "@ton/crypto": "^3.2.0", "@ton/ton": "^13.11.2", "@tonconnect/ui-react": "^2.0.5", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/node-telegram-bot-api": "^0.64.9", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.14", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "next": "15.3.3", "node-telegram-bot-api": "^0.66.0", "postcss": "^8.4.24", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^3.4.0", "typescript": "^5"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.3.3", "tsx": "^4.19.4"}}