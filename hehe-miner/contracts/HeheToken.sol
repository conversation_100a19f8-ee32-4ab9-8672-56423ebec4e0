// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title HEHE Token
 * @dev ERC20 token for Hehe Miner Telegram game with TGE mechanics
 *
 * Total Supply: 500,000,000 HEHE
 * Decimals: 18
 *
 * Allocation:
 * - 70% (350M) - Airdrop Community (No vesting, distributed at TGE)
 * - 10% (50M) - Partners & Ecosystem (6-12 month vesting)
 * - 5% (25M) - Team (24 month linear vesting)
 * - 10% (50M) - Liquidity Pool (Available at TGE)
 * - 5% (25M) - Treasury/Development (Available at TGE)
 *
 * TGE (Token Generation Event) Flow:
 * 1. Deploy contract (no tokens minted initially)
 * 2. Take snapshot of game users
 * 3. Set airdrop allocations based on snapshot
 * 4. Execute TGE - mint and distribute tokens
 */
contract HeheToken is ERC20, ERC20Burnable, Pausable, Ownable, ReentrancyGuard {

    // Token constants
    uint256 public constant TOTAL_SUPPLY = 500_000_000 * 10**18; // 500 million tokens
    uint256 public constant AIRDROP_ALLOCATION = 350_000_000 * 10**18; // 70%
    uint256 public constant PARTNERS_ALLOCATION = 50_000_000 * 10**18; // 10%
    uint256 public constant TEAM_ALLOCATION = 25_000_000 * 10**18; // 5%
    uint256 public constant LIQUIDITY_ALLOCATION = 50_000_000 * 10**18; // 10%
    uint256 public constant TREASURY_ALLOCATION = 25_000_000 * 10**18; // 5%

    // TGE state
    bool public tgeExecuted = false;
    uint256 public tgeTimestamp;

    // Allocation wallets
    address public airdropWallet;
    address public partnersWallet;
    address public teamWallet;
    address public liquidityWallet;
    address public treasuryWallet;

    // Airdrop management
    mapping(address => bool) public airdropClaimed;
    mapping(address => uint256) public airdropAmount;
    mapping(address => uint256) public snapshotBalance; // User's game balance at snapshot
    bool public airdropActive = false;
    uint256 public totalSnapshotUsers = 0;
    uint256 public snapshotTimestamp;
    
    // Game integration
    mapping(address => bool) public gameContracts;
    mapping(address => uint256) public lastMiningClaim;
    uint256 public miningCooldown = 4 hours;
    
    // Events
    event TGEExecuted(uint256 timestamp, uint256 totalSupply);
    event SnapshotTaken(uint256 timestamp, uint256 totalUsers);
    event AirdropClaimed(address indexed user, uint256 amount);
    event MiningReward(address indexed user, uint256 amount);
    event GameContractAdded(address indexed gameContract);
    event GameContractRemoved(address indexed gameContract);

    constructor(
        address _airdropWallet,
        address _partnersWallet,
        address _teamWallet,
        address _liquidityWallet,
        address _treasuryWallet
    ) ERC20("Hehe Token", "HEHE") {
        require(_airdropWallet != address(0), "Invalid airdrop wallet");
        require(_partnersWallet != address(0), "Invalid partners wallet");
        require(_teamWallet != address(0), "Invalid team wallet");
        require(_liquidityWallet != address(0), "Invalid liquidity wallet");
        require(_treasuryWallet != address(0), "Invalid treasury wallet");

        airdropWallet = _airdropWallet;
        partnersWallet = _partnersWallet;
        teamWallet = _teamWallet;
        liquidityWallet = _liquidityWallet;
        treasuryWallet = _treasuryWallet;

        // No tokens minted initially - wait for TGE
        // Total supply will be minted during executeTGE()
    }

    /**
     * @dev Take snapshot of user balances (called before TGE)
     */
    function takeSnapshot(address[] calldata users, uint256[] calldata balances) external onlyOwner {
        require(!tgeExecuted, "TGE already executed");
        require(users.length == balances.length, "Arrays length mismatch");

        for (uint256 i = 0; i < users.length; i++) {
            snapshotBalance[users[i]] = balances[i];
        }

        totalSnapshotUsers += users.length;
        snapshotTimestamp = block.timestamp;

        emit SnapshotTaken(block.timestamp, totalSnapshotUsers);
    }

    /**
     * @dev Execute TGE - mint all tokens and set up allocations
     */
    function executeTGE() external onlyOwner {
        require(!tgeExecuted, "TGE already executed");
        require(snapshotTimestamp > 0, "Snapshot not taken");

        tgeExecuted = true;
        tgeTimestamp = block.timestamp;

        // Mint all allocations
        _mint(airdropWallet, AIRDROP_ALLOCATION);
        _mint(partnersWallet, PARTNERS_ALLOCATION);
        _mint(teamWallet, TEAM_ALLOCATION);
        _mint(liquidityWallet, LIQUIDITY_ALLOCATION);
        _mint(treasuryWallet, TREASURY_ALLOCATION);

        // Activate airdrop
        airdropActive = true;

        emit TGEExecuted(block.timestamp, TOTAL_SUPPLY);
    }
    
    /**
     * @dev Add authorized game contract
     */
    function addGameContract(address _gameContract) external onlyOwner {
        require(_gameContract != address(0), "Invalid game contract");
        gameContracts[_gameContract] = true;
        emit GameContractAdded(_gameContract);
    }
    
    /**
     * @dev Remove authorized game contract
     */
    function removeGameContract(address _gameContract) external onlyOwner {
        gameContracts[_gameContract] = false;
        emit GameContractRemoved(_gameContract);
    }
    
    /**
     * @dev Set airdrop amounts for users
     */
    function setAirdropAmounts(address[] calldata users, uint256[] calldata amounts) external onlyOwner {
        require(users.length == amounts.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < users.length; i++) {
            airdropAmount[users[i]] = amounts[i];
        }
    }
    
    /**
     * @dev Activate/deactivate airdrop
     */
    function setAirdropActive(bool _active) external onlyOwner {
        airdropActive = _active;
    }
    
    /**
     * @dev Calculate airdrop amount based on snapshot balance
     */
    function calculateAirdropAmount(address user) public view returns (uint256) {
        if (snapshotBalance[user] == 0) return 0;

        // Example calculation: 1 HEHE token per 1 game point
        // You can adjust this formula based on your tokenomics
        return snapshotBalance[user] * 10**18; // Convert to 18 decimals
    }

    /**
     * @dev Claim airdrop tokens based on snapshot
     */
    function claimAirdrop() external nonReentrant whenNotPaused {
        require(tgeExecuted, "TGE not executed yet");
        require(airdropActive, "Airdrop not active");
        require(!airdropClaimed[msg.sender], "Already claimed");
        require(snapshotBalance[msg.sender] > 0, "No snapshot balance");

        uint256 amount = calculateAirdropAmount(msg.sender);
        require(amount > 0, "No airdrop allocation");

        airdropClaimed[msg.sender] = true;

        // Transfer from airdrop wallet
        _transfer(airdropWallet, msg.sender, amount);

        emit AirdropClaimed(msg.sender, amount);
    }

    /**
     * @dev Check if user is eligible for airdrop
     */
    function isEligibleForAirdrop(address user) external view returns (bool, uint256) {
        if (airdropClaimed[user] || snapshotBalance[user] == 0) {
            return (false, 0);
        }
        return (true, calculateAirdropAmount(user));
    }
    
    /**
     * @dev Mining reward function (called by game contracts)
     */
    function mintMiningReward(address user, uint256 amount) external nonReentrant {
        require(gameContracts[msg.sender], "Unauthorized game contract");
        require(user != address(0), "Invalid user address");
        require(amount > 0, "Invalid amount");
        require(
            block.timestamp >= lastMiningClaim[user] + miningCooldown,
            "Mining cooldown active"
        );
        
        lastMiningClaim[user] = block.timestamp;
        
        // Mint new tokens for mining rewards (from treasury allocation)
        _transfer(treasuryWallet, user, amount);
        
        emit MiningReward(user, amount);
    }
    
    /**
     * @dev Set mining cooldown period
     */
    function setMiningCooldown(uint256 _cooldown) external onlyOwner {
        miningCooldown = _cooldown;
    }
    
    /**
     * @dev Check if user can claim mining reward
     */
    function canClaimMining(address user) external view returns (bool) {
        return block.timestamp >= lastMiningClaim[user] + miningCooldown;
    }
    
    /**
     * @dev Get time until next mining claim
     */
    function timeUntilNextMining(address user) external view returns (uint256) {
        uint256 nextClaim = lastMiningClaim[user] + miningCooldown;
        if (block.timestamp >= nextClaim) {
            return 0;
        }
        return nextClaim - block.timestamp;
    }
    
    /**
     * @dev Pause contract (emergency)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Override transfer to add pause functionality
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override whenNotPaused {
        super._beforeTokenTransfer(from, to, amount);
    }
    
    /**
     * @dev Emergency withdrawal (only owner)
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(amount);
        } else {
            IERC20(token).transfer(owner(), amount);
        }
    }
}
