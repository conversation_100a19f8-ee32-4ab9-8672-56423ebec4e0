// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title HEHE Token
 * @dev ERC20 token for Hehe Miner Telegram game
 * 
 * Total Supply: 500,000,000 HEHE
 * Decimals: 18
 * 
 * Allocation:
 * - 70% (350M) - Airdrop Community (No vesting)
 * - 10% (50M) - Partners & Ecosystem
 * - 5% (25M) - Team
 * - 10% (50M) - Liquidity Pool
 * - 5% (25M) - Treasury/Development
 */
contract HeheToken is ERC20, ERC20Burnable, Pausable, Ownable, ReentrancyGuard {
    
    // Token constants
    uint256 public constant TOTAL_SUPPLY = 500_000_000 * 10**18; // 500 million tokens
    uint256 public constant AIRDROP_ALLOCATION = 350_000_000 * 10**18; // 70%
    uint256 public constant PARTNERS_ALLOCATION = 50_000_000 * 10**18; // 10%
    uint256 public constant TEAM_ALLOCATION = 25_000_000 * 10**18; // 5%
    uint256 public constant LIQUIDITY_ALLOCATION = 50_000_000 * 10**18; // 10%
    uint256 public constant TREASURY_ALLOCATION = 25_000_000 * 10**18; // 5%
    
    // Allocation wallets
    address public airdropWallet;
    address public partnersWallet;
    address public teamWallet;
    address public liquidityWallet;
    address public treasuryWallet;
    
    // Airdrop management
    mapping(address => bool) public airdropClaimed;
    mapping(address => uint256) public airdropAmount;
    bool public airdropActive = false;
    
    // Game integration
    mapping(address => bool) public gameContracts;
    mapping(address => uint256) public lastMiningClaim;
    uint256 public miningCooldown = 4 hours;
    
    // Events
    event AirdropClaimed(address indexed user, uint256 amount);
    event MiningReward(address indexed user, uint256 amount);
    event GameContractAdded(address indexed gameContract);
    event GameContractRemoved(address indexed gameContract);
    
    constructor(
        address _airdropWallet,
        address _partnersWallet,
        address _teamWallet,
        address _liquidityWallet,
        address _treasuryWallet
    ) ERC20("Hehe Token", "HEHE") {
        require(_airdropWallet != address(0), "Invalid airdrop wallet");
        require(_partnersWallet != address(0), "Invalid partners wallet");
        require(_teamWallet != address(0), "Invalid team wallet");
        require(_liquidityWallet != address(0), "Invalid liquidity wallet");
        require(_treasuryWallet != address(0), "Invalid treasury wallet");
        
        airdropWallet = _airdropWallet;
        partnersWallet = _partnersWallet;
        teamWallet = _teamWallet;
        liquidityWallet = _liquidityWallet;
        treasuryWallet = _treasuryWallet;
        
        // Mint initial allocations
        _mint(_airdropWallet, AIRDROP_ALLOCATION);
        _mint(_partnersWallet, PARTNERS_ALLOCATION);
        _mint(_teamWallet, TEAM_ALLOCATION);
        _mint(_liquidityWallet, LIQUIDITY_ALLOCATION);
        _mint(_treasuryWallet, TREASURY_ALLOCATION);
    }
    
    /**
     * @dev Add authorized game contract
     */
    function addGameContract(address _gameContract) external onlyOwner {
        require(_gameContract != address(0), "Invalid game contract");
        gameContracts[_gameContract] = true;
        emit GameContractAdded(_gameContract);
    }
    
    /**
     * @dev Remove authorized game contract
     */
    function removeGameContract(address _gameContract) external onlyOwner {
        gameContracts[_gameContract] = false;
        emit GameContractRemoved(_gameContract);
    }
    
    /**
     * @dev Set airdrop amounts for users
     */
    function setAirdropAmounts(address[] calldata users, uint256[] calldata amounts) external onlyOwner {
        require(users.length == amounts.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < users.length; i++) {
            airdropAmount[users[i]] = amounts[i];
        }
    }
    
    /**
     * @dev Activate/deactivate airdrop
     */
    function setAirdropActive(bool _active) external onlyOwner {
        airdropActive = _active;
    }
    
    /**
     * @dev Claim airdrop tokens
     */
    function claimAirdrop() external nonReentrant whenNotPaused {
        require(airdropActive, "Airdrop not active");
        require(!airdropClaimed[msg.sender], "Already claimed");
        require(airdropAmount[msg.sender] > 0, "No airdrop allocation");
        
        uint256 amount = airdropAmount[msg.sender];
        airdropClaimed[msg.sender] = true;
        
        // Transfer from airdrop wallet
        _transfer(airdropWallet, msg.sender, amount);
        
        emit AirdropClaimed(msg.sender, amount);
    }
    
    /**
     * @dev Mining reward function (called by game contracts)
     */
    function mintMiningReward(address user, uint256 amount) external nonReentrant {
        require(gameContracts[msg.sender], "Unauthorized game contract");
        require(user != address(0), "Invalid user address");
        require(amount > 0, "Invalid amount");
        require(
            block.timestamp >= lastMiningClaim[user] + miningCooldown,
            "Mining cooldown active"
        );
        
        lastMiningClaim[user] = block.timestamp;
        
        // Mint new tokens for mining rewards (from treasury allocation)
        _transfer(treasuryWallet, user, amount);
        
        emit MiningReward(user, amount);
    }
    
    /**
     * @dev Set mining cooldown period
     */
    function setMiningCooldown(uint256 _cooldown) external onlyOwner {
        miningCooldown = _cooldown;
    }
    
    /**
     * @dev Check if user can claim mining reward
     */
    function canClaimMining(address user) external view returns (bool) {
        return block.timestamp >= lastMiningClaim[user] + miningCooldown;
    }
    
    /**
     * @dev Get time until next mining claim
     */
    function timeUntilNextMining(address user) external view returns (uint256) {
        uint256 nextClaim = lastMiningClaim[user] + miningCooldown;
        if (block.timestamp >= nextClaim) {
            return 0;
        }
        return nextClaim - block.timestamp;
    }
    
    /**
     * @dev Pause contract (emergency)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Override transfer to add pause functionality
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override whenNotPaused {
        super._beforeTokenTransfer(from, to, amount);
    }
    
    /**
     * @dev Emergency withdrawal (only owner)
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(amount);
        } else {
            IERC20(token).transfer(owner(), amount);
        }
    }
}
