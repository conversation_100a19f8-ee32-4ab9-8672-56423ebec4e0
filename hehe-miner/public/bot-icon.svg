<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3fff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2eee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1ddd;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="helmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Main background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="#FFFFFF" stroke-width="8" filter="url(#shadow)"/>
  
  <!-- Inner circle for depth -->
  <circle cx="256" cy="256" r="220" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.3"/>
  
  <!-- Main miner character -->
  <g transform="translate(256, 256)">
    <!-- Miner helmet -->
    <ellipse cx="0" cy="-80" rx="80" ry="60" fill="url(#helmetGradient)" stroke="#1A202C" stroke-width="4"/>
    
    <!-- Helmet light -->
    <circle cx="0" cy="-110" r="20" fill="#FFD700" stroke="#FFA500" stroke-width="3" filter="url(#glow)"/>
    <circle cx="0" cy="-110" r="12" fill="#FFFF99"/>
    <circle cx="0" cy="-110" r="6" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Face -->
    <circle cx="0" cy="-20" r="60" fill="#FFD700" stroke="#FFA500" stroke-width="4"/>
    
    <!-- Eyes -->
    <ellipse cx="-20" cy="-35" rx="8" ry="12" fill="#2D3748"/>
    <ellipse cx="20" cy="-35" rx="8" ry="12" fill="#2D3748"/>
    <circle cx="-18" cy="-38" r="3" fill="#FFFFFF"/>
    <circle cx="22" cy="-38" r="3" fill="#FFFFFF"/>
    
    <!-- Smile -->
    <path d="M -25 -5 Q 0 15 25 -5" stroke="#2D3748" stroke-width="4" fill="none" stroke-linecap="round"/>
    
    <!-- Body -->
    <ellipse cx="0" cy="60" rx="50" ry="80" fill="#4A5568" stroke="#2D3748" stroke-width="4"/>
    
    <!-- Arms holding pickaxe -->
    <ellipse cx="-65" cy="40" rx="20" ry="45" fill="#4A5568" stroke="#2D3748" stroke-width="3"/>
    <ellipse cx="65" cy="40" rx="20" ry="45" fill="#4A5568" stroke="#2D3748" stroke-width="3"/>
  </g>
  
  <!-- Pickaxe -->
  <g transform="translate(320, 200) rotate(15)">
    <!-- Handle -->
    <rect x="-6" y="0" width="12" height="100" fill="#8B4513" stroke="#654321" stroke-width="3"/>
    
    <!-- Pickaxe head -->
    <polygon points="-30,-15 30,-15 35,-5 30,5 -30,5 -35,-5" fill="#C0C0C0" stroke="#808080" stroke-width="3"/>
    <polygon points="30,-10 55,-6 60,0 55,6 30,10" fill="#C0C0C0" stroke="#808080" stroke-width="3"/>
    
    <!-- Shine on pickaxe -->
    <polygon points="-25,-10 20,-10 22,-5 20,0 -25,0" fill="#E0E0E0" opacity="0.7"/>
  </g>
  
  <!-- HEHE coins floating around -->
  <g opacity="0.9">
    <circle cx="150" cy="180" r="18" fill="#FFD700" stroke="#FFA500" stroke-width="3" filter="url(#glow)"/>
    <text x="150" y="187" font-family="Arial Black" font-size="16" font-weight="bold" text-anchor="middle" fill="#2D3748">H</text>
    
    <circle cx="380" cy="150" r="18" fill="#FFD700" stroke="#FFA500" stroke-width="3" filter="url(#glow)"/>
    <text x="380" y="157" font-family="Arial Black" font-size="16" font-weight="bold" text-anchor="middle" fill="#2D3748">E</text>
    
    <circle cx="120" cy="350" r="18" fill="#FFD700" stroke="#FFA500" stroke-width="3" filter="url(#glow)"/>
    <text x="120" y="357" font-family="Arial Black" font-size="16" font-weight="bold" text-anchor="middle" fill="#2D3748">H</text>
    
    <circle cx="400" cy="320" r="18" fill="#FFD700" stroke="#FFA500" stroke-width="3" filter="url(#glow)"/>
    <text x="400" y="327" font-family="Arial Black" font-size="16" font-weight="bold" text-anchor="middle" fill="#2D3748">E</text>
  </g>
  
  <!-- Sparkle effects -->
  <g opacity="0.8">
    <polygon points="180,120 186,135 201,129 186,144 180,159 174,144 159,129 174,135" fill="#FFFFFF" filter="url(#glow)"/>
    <polygon points="340,100 344,110 354,106 344,116 340,126 336,116 326,106 336,110" fill="#FFFFFF" filter="url(#glow)"/>
    <polygon points="100,250 104,260 114,256 104,266 100,276 96,266 86,256 96,260" fill="#FFFFFF" filter="url(#glow)"/>
    <polygon points="420,280 424,290 434,286 424,296 420,306 416,296 406,286 416,290" fill="#FFFFFF" filter="url(#glow)"/>
  </g>
  
  <!-- Bottom text -->
  <text x="256" y="450" font-family="Arial Black, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="#FFFFFF" stroke="#2D3748" stroke-width="2" filter="url(#shadow)">HEHE MINER</text>
</svg>
