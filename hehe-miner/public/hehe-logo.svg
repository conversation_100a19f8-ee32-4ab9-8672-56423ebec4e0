<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="helmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main background -->
  <circle cx="60" cy="60" r="58" fill="url(#bgGradient)" stroke="#2D3748" stroke-width="4"/>
  
  <!-- Miner helmet -->
  <ellipse cx="60" cy="45" rx="25" ry="20" fill="url(#helmetGradient)" stroke="#1A202C" stroke-width="2"/>
  
  <!-- Helmet light -->
  <circle cx="60" cy="35" r="6" fill="#FFD700" stroke="#FFA500" stroke-width="1"/>
  <circle cx="60" cy="35" r="3" fill="#FFFF99"/>
  
  <!-- Face -->
  <circle cx="60" cy="65" r="20" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
  
  <!-- Eyes -->
  <ellipse cx="52" cy="60" rx="3" ry="4" fill="#2D3748"/>
  <ellipse cx="68" cy="60" rx="3" ry="4" fill="#2D3748"/>
  
  <!-- Smile -->
  <path d="M 50 70 Q 60 80 70 70" stroke="#2D3748" stroke-width="3" fill="none" stroke-linecap="round"/>
  
  <!-- Text HEHE -->
  <text x="60" y="100" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#2D3748">HEHE</text>
  <text x="60" y="115" font-family="Arial, sans-serif" font-size="10" font-weight="bold" text-anchor="middle" fill="#4A5568">MINER</text>
</svg>
