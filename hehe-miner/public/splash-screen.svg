<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3fff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2eee;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1ddd;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="helmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2D3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="pickaxeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B4513;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#654321;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main background -->
  <rect width="512" height="512" fill="url(#bgGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="100" cy="100" r="20" fill="#3fff" opacity="0.3"/>
  <circle cx="412" cy="150" r="15" fill="#3fff" opacity="0.4"/>
  <circle cx="80" cy="400" r="25" fill="#3fff" opacity="0.2"/>
  <circle cx="450" cy="380" r="18" fill="#3fff" opacity="0.3"/>
  
  <!-- Main miner character -->
  <g transform="translate(256, 200)">
    <!-- Miner helmet -->
    <ellipse cx="0" cy="-50" rx="60" ry="45" fill="url(#helmetGradient)" stroke="#1A202C" stroke-width="3"/>
    
    <!-- Helmet light -->
    <circle cx="0" cy="-70" r="15" fill="#FFD700" stroke="#FFA500" stroke-width="2" filter="url(#glow)"/>
    <circle cx="0" cy="-70" r="8" fill="#FFFF99"/>
    
    <!-- Face -->
    <circle cx="0" cy="0" r="45" fill="#FFD700" stroke="#FFA500" stroke-width="3"/>
    
    <!-- Eyes -->
    <ellipse cx="-15" cy="-10" rx="6" ry="8" fill="#2D3748"/>
    <ellipse cx="15" cy="-10" rx="6" ry="8" fill="#2D3748"/>
    <circle cx="-13" cy="-12" r="2" fill="#FFFFFF"/>
    <circle cx="17" cy="-12" r="2" fill="#FFFFFF"/>
    
    <!-- Smile -->
    <path d="M -20 10 Q 0 25 20 10" stroke="#2D3748" stroke-width="3" fill="none" stroke-linecap="round"/>
    
    <!-- Body -->
    <ellipse cx="0" cy="80" rx="40" ry="60" fill="#4A5568" stroke="#2D3748" stroke-width="3"/>
    
    <!-- Arms -->
    <ellipse cx="-50" cy="60" rx="15" ry="35" fill="#4A5568" stroke="#2D3748" stroke-width="2"/>
    <ellipse cx="50" cy="60" rx="15" ry="35" fill="#4A5568" stroke="#2D3748" stroke-width="2"/>
  </g>
  
  <!-- Pickaxe -->
  <g transform="translate(350, 280) rotate(25)">
    <!-- Handle -->
    <rect x="-5" y="0" width="10" height="80" fill="url(#pickaxeGradient)" stroke="#4A2C17" stroke-width="2"/>
    
    <!-- Pickaxe head -->
    <polygon points="-25,-10 25,-10 30,0 25,10 -25,10 -30,0" fill="#C0C0C0" stroke="#808080" stroke-width="2"/>
    <polygon points="25,-8 45,-5 50,0 45,5 25,8" fill="#C0C0C0" stroke="#808080" stroke-width="2"/>
  </g>
  
  <!-- HEHE text -->
  <g transform="translate(256, 400)">
    <text x="0" y="0" font-family="Arial Black, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="#FFFFFF" stroke="#2D3748" stroke-width="2">HEHE</text>
    <text x="0" y="35" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#FFFFFF" opacity="0.9">MINER</text>
  </g>
  
  <!-- Floating coins -->
  <g opacity="0.8">
    <circle cx="150" cy="250" r="12" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
    <text x="150" y="255" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#2D3748">H</text>
    
    <circle cx="380" cy="200" r="12" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
    <text x="380" y="205" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#2D3748">E</text>
    
    <circle cx="120" cy="320" r="12" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
    <text x="120" y="325" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#2D3748">H</text>
    
    <circle cx="400" cy="300" r="12" fill="#FFD700" stroke="#FFA500" stroke-width="2"/>
    <text x="400" y="305" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#2D3748">E</text>
  </g>
  
  <!-- Sparkle effects -->
  <g opacity="0.6">
    <polygon points="180,150 185,160 195,155 185,165 180,175 175,165 165,155 175,160" fill="#FFFFFF"/>
    <polygon points="320,120 323,127 330,124 323,131 320,138 317,131 310,124 317,127" fill="#FFFFFF"/>
    <polygon points="100,200 103,207 110,204 103,211 100,218 97,211 90,204 97,207" fill="#FFFFFF"/>
    <polygon points="420,250 423,257 430,254 423,261 420,268 417,261 410,254 417,257" fill="#FFFFFF"/>
  </g>
</svg>
