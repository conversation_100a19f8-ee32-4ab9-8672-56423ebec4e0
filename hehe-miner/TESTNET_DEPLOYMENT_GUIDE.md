# 🧪 HEHE Token Testnet Deployment Guide

Complete guide for deploying and testing HEHE token on Polygon Mumbai testnet before mainnet launch.

## 🎯 **TGE (Token Generation Event) Flow**

### **Phase 1: Pre-TGE (Current)**
- ✅ Deploy contract (no tokens minted)
- ✅ Game runs normally on Telegram
- ✅ Users accumulate points/rewards
- ✅ Take snapshot of user balances

### **Phase 2: TGE Execution**
- 🎉 Execute TGE function
- 💰 Mint all 500M tokens
- 📦 Distribute to allocation wallets
- 🎁 Activate airdrop claims

### **Phase 3: Post-TGE**
- 🚀 Users claim airdrops
- 💧 Add liquidity to DEX
- 📈 Trading begins
- 🔗 Full game integration

## 🛠️ **Testnet Setup**

### **Step 1: Get Mumbai Testnet MATIC**

1. **Add Mumbai to MetaMask:**
   - Network Name: `Polygon Mumbai`
   - RPC URL: `https://rpc-mumbai.maticvigil.com/`
   - Chain ID: `80001`
   - Symbol: `MATIC`
   - Explorer: `https://mumbai.polygonscan.com/`

2. **Get Test MATIC:**
   - Visit: https://faucet.polygon.technology/
   - Enter your wallet address
   - Get free test MATIC

### **Step 2: Environment Setup**

```bash
# Install dependencies
npm install --save-dev hardhat @nomiclabs/hardhat-waffle @nomiclabs/hardhat-etherscan @openzeppelin/contracts ethers dotenv

# Create environment file
cp .env.contracts.example .env.contracts
```

**Edit `.env.contracts`:**
```env
PRIVATE_KEY=your-wallet-private-key
POLYGONSCAN_API_KEY=your-polygonscan-api-key
AIRDROP_WALLET=your-airdrop-wallet-address
PARTNERS_WALLET=your-partners-wallet-address
TEAM_WALLET=your-team-wallet-address
LIQUIDITY_WALLET=your-liquidity-wallet-address
TREASURY_WALLET=your-treasury-wallet-address
```

### **Step 3: Deploy to Mumbai Testnet**

```bash
# Compile contracts
npx hardhat compile

# Deploy to Mumbai testnet
npx hardhat run scripts/deploy-polygon.js --network mumbai

# Verify contract (optional)
npx hardhat verify --network mumbai CONTRACT_ADDRESS
```

### **Step 4: Test TGE Process**

```bash
# Update CONTRACT_ADDRESS in execute-tge.js
# Then execute TGE
npx hardhat run scripts/execute-tge.js --network mumbai
```

## 📊 **Contract Functions**

### **Owner Functions (Pre-TGE)**
```solidity
// Take snapshot of user balances
takeSnapshot(address[] users, uint256[] balances)

// Execute TGE - mint all tokens
executeTGE()

// Add authorized game contracts
addGameContract(address gameContract)
```

### **User Functions (Post-TGE)**
```solidity
// Check airdrop eligibility
isEligibleForAirdrop(address user) → (bool, uint256)

// Claim airdrop tokens
claimAirdrop()

// Check mining cooldown
canClaimMining(address user) → bool
timeUntilNextMining(address user) → uint256
```

### **Game Integration Functions**
```solidity
// Mint mining rewards (called by game contracts)
mintMiningReward(address user, uint256 amount)
```

## 🧪 **Testing Checklist**

### **Pre-TGE Testing**
- [ ] Contract deploys successfully
- [ ] Total supply is 0
- [ ] TGE not executed
- [ ] Snapshot functions work
- [ ] Owner controls work

### **TGE Testing**
- [ ] Snapshot can be taken
- [ ] TGE executes successfully
- [ ] 500M tokens minted
- [ ] Correct allocation to wallets
- [ ] Airdrop activated

### **Post-TGE Testing**
- [ ] Users can check eligibility
- [ ] Airdrop claims work
- [ ] Mining rewards work
- [ ] Transfer functions work
- [ ] Game integration works

## 🎮 **Game Integration**

### **Frontend Integration (Telegram App)**

```javascript
// Check if user is eligible for airdrop
async function checkAirdropEligibility(userAddress) {
  const [eligible, amount] = await heheContract.isEligibleForAirdrop(userAddress);
  return { eligible, amount: ethers.utils.formatEther(amount) };
}

// Claim airdrop
async function claimAirdrop() {
  const tx = await heheContract.claimAirdrop();
  await tx.wait();
  return tx.hash;
}

// Check token balance
async function getTokenBalance(userAddress) {
  const balance = await heheContract.balanceOf(userAddress);
  return ethers.utils.formatEther(balance);
}
```

### **Backend Integration (API)**

```javascript
// Take snapshot from game database
async function takeGameSnapshot() {
  const users = await getUsersFromDatabase();
  const addresses = users.map(u => u.walletAddress);
  const balances = users.map(u => ethers.utils.parseEther(u.gameBalance.toString()));
  
  const tx = await heheContract.takeSnapshot(addresses, balances);
  await tx.wait();
}
```

## 📈 **Tokenomics Testing**

### **Allocation Verification**
- **Airdrop (70%)**: 350,000,000 HEHE
- **Partners (10%)**: 50,000,000 HEHE  
- **Team (5%)**: 25,000,000 HEHE
- **Liquidity (10%)**: 50,000,000 HEHE
- **Treasury (5%)**: 25,000,000 HEHE

### **Airdrop Formula**
```
User Airdrop = Game Points × 1 HEHE per point
```

### **Mining Rewards**
- 4 HEHE tokens per 4-hour mining session
- Cooldown enforced by smart contract
- Tokens minted from treasury allocation

## 🚀 **Mainnet Migration**

Once testnet testing is complete:

1. **Deploy to Polygon Mainnet:**
   ```bash
   npx hardhat run scripts/deploy-polygon.js --network polygon
   ```

2. **Take Real Snapshot:**
   - Export user data from production database
   - Call `takeSnapshot()` with real user data

3. **Execute TGE:**
   - Announce TGE date to community
   - Execute `executeTGE()` at announced time
   - Users can immediately claim airdrops

4. **Add Liquidity:**
   - Create HEHE/MATIC pair on QuickSwap
   - Add initial liquidity from liquidity allocation

## ⚠️ **Important Notes**

- **No tokens exist until TGE** - contract starts with 0 supply
- **Snapshot is permanent** - cannot be changed after TGE
- **Airdrop claims are one-time** - users cannot claim twice
- **Game integration** - authorize game contracts for mining rewards
- **Security** - contract is pausable for emergencies

## 🔗 **Useful Links**

- **Mumbai Faucet**: https://faucet.polygon.technology/
- **Mumbai Explorer**: https://mumbai.polygonscan.com/
- **Polygon Docs**: https://docs.polygon.technology/
- **OpenZeppelin**: https://docs.openzeppelin.com/

---

**Ready to test? Start with Mumbai testnet deployment!** 🚀
