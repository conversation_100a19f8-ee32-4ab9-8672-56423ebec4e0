exports.id=804,exports.ids=[804],exports.modules={1323:(e,t)=>{"use strict";Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return i}});let n=r(475),o=r(8364);function i(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,"/website"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1443:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(8364);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6691:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_FAST_REFRESH:function(){return l},ACTION_NAVIGATE:function(){return i},ACTION_PREFETCH:function(){return c},ACTION_REFRESH:function(){return o},ACTION_RESTORE:function(){return a},ACTION_SERVER_ACTION:function(){return u},ACTION_SERVER_PATCH:function(){return s},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return r},isThenable:function(){return d}});let o="refresh",i="navigate",a="restore",s="server-patch",c="prefetch",l="fast-refresh",u="server-action";function d(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4318:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(8364),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9142:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(8106);function o(e){return(0,n.pathHasPrefix)(e,"/website")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9577:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return y}});let n=r(167),o=r(997),i=n._(r(6689)),a=r(1401),s=r(2045),c=r(7420),l=r(7201),u=r(1443),d=r(5469),f=r(7443),p=r(2905),h=r(4318),g=r(953),m=r(6691);function _(e){return"string"==typeof e?e:(0,c.formatUrl)(e)}let y=i.default.forwardRef(function(e,t){let r,n;let{href:c,as:y,children:b,prefetch:v=null,passHref:E,replace:I,shallow:P,scroll:x,locale:R,onClick:O,onMouseEnter:j,onTouchStart:C,legacyBehavior:T=!1,...A}=e;r=b,T&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let S=i.default.useContext(d.RouterContext),M=i.default.useContext(f.AppRouterContext),L=null!=S?S:M,k=!S,U=!1!==v,N=null===v?m.PrefetchKind.AUTO:m.PrefetchKind.FULL,{href:w,as:G}=i.default.useMemo(()=>{if(!S){let e=_(c);return{href:e,as:y?_(y):e}}let[e,t]=(0,a.resolveHref)(S,c,!0);return{href:e,as:y?(0,a.resolveHref)(S,y):t||e}},[S,c,y]),D=i.default.useRef(w),W=i.default.useRef(G);T&&(n=i.default.Children.only(r));let B=T?n&&"object"==typeof n&&n.ref:t,[F,H,X]=(0,p.useIntersection)({rootMargin:"200px"}),V=i.default.useCallback(e=>{(W.current!==G||D.current!==w)&&(X(),W.current=G,D.current=w),F(e),B&&("function"==typeof B?B(e):"object"==typeof B&&(B.current=e))},[G,B,w,X,F]);i.default.useEffect(()=>{},[G,w,H,R,U,null==S?void 0:S.locale,L,k,N]);let Y={ref:V,onClick(e){T||"function"!=typeof O||O(e),T&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&!e.defaultPrevented&&function(e,t,r,n,o,a,c,l,u){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!u&&!(0,s.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==c||c;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:a,locale:l,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})};u?i.default.startTransition(f):f()}(e,L,w,G,I,P,x,R,k)},onMouseEnter(e){T||"function"!=typeof j||j(e),T&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){T||"function"!=typeof C||C(e),T&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,l.isAbsoluteUrl)(G))Y.href=G;else if(!T||E||"a"===n.type&&!("href"in n.props)){let e=void 0!==R?R:null==S?void 0:S.locale,t=(null==S?void 0:S.isLocaleDomain)&&(0,h.getDomainLocale)(G,e,null==S?void 0:S.locales,null==S?void 0:S.domainLocales);Y.href=t||(0,g.addBasePath)((0,u.addLocale)(G,e,null==S?void 0:S.defaultLocale))}return T?i.default.cloneElement(n,Y):(0,o.jsx)("a",{...A,...Y,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8364:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return i}});let n=r(9362),o=r(2107),i=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:i}=(0,o.parsePath)(e);return/\.[^/]+\/?$/.test(t)?""+(0,n.removeTrailingSlash)(t)+r+i:t.endsWith("/")?""+t+r+i:t+"/"+r+i};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3815:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1401:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(8260),o=r(7420),i=r(3061),a=r(7201),s=r(8364),c=r(2045),l=r(5722),u=r(4321);function d(e,t,r){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,a.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,c.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,s.normalizePathTrailingSlash)(e.pathname);let t="";if((0,l.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:a,params:s}=(0,u.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,o.formatWithValidation)({pathname:a,hash:e.hash,query:(0,i.omit)(r,s)}))}let a=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return c}});let n=r(6689),o=r(3815),i="function"==typeof IntersectionObserver,a=new Map,s=[];function c(e){let{rootRef:t,rootMargin:r,disabled:c}=e,l=c||!i,[u,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(i){if(l||u)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:i}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=s.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=a.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},s.push(r),a.set(r,t),t}(r);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),a.delete(n);let e=s.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&s.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!u){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[l,r,t,u,f.current]),[p,u,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1885:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},475:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(2107);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:i}=(0,n.parsePath)(e);return""+t+r+o+i}},7420:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(8760)._(r(8260)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",a=e.pathname||"",s=e.hash||"",c=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),c&&"object"==typeof c&&(c=String(n.urlQueryToSearchParams(c)));let u=e.search||c&&"?"+c||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==l?(l="//"+(l||""),a&&"/"!==a[0]&&(a="/"+a)):l||(l=""),s&&"#"!==s[0]&&(s="#"+s),u&&"?"!==u[0]&&(u="?"+u),""+i+l+(a=a.replace(/[?#]/g,encodeURIComponent))+(u=u.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return i(e)}},4321:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return i}});let n=r(122),o=r(2437);function i(e,t,r){let i="",a=(0,o.getRouteRegex)(e),s=a.groups,c=(t!==e?(0,n.getRouteMatcher)(a)(t):"")||r;i=e;let l=Object.keys(s);return l.every(e=>{let t=c[e]||"",{repeat:r,optional:n}=s[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in c)&&(i=i.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(i=""),{params:l,result:i}}},2045:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(7201),o=r(9142);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},3061:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},2107:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},8106:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(2107);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},8260:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9362:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},122:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(7201);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>i(e)):t.repeat?[i(n)]:i(n))}),a}}},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return p},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return l},parseParameter:function(){return s}});let n=r(2350),o=r(2407),i=r(1885),a=r(9362);function s(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=o.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:o,repeat:c}=s(a[1]);return r[e]={pos:n++,repeat:c,optional:o},"/"+(0,i.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,i.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:o}=s(a[1]);return r[e]={pos:n++,repeat:t,optional:o},t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function l(e){let{parameterizedRoute:t,groups:r}=c(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function u(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:o,keyPrefix:a}=e,{key:c,optional:l,repeat:u}=s(n),d=c.replace(/\W/g,"");a&&(d=""+a+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),a?o[d]=""+a+c:o[d]=c;let p=t?(0,i.escapeStringRegexp)(t):"";return u?l?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let s=(0,a.removeTrailingSlash)(e).slice(1).split("/"),c=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),l={};return{namedParameterizedRoute:s.map(e=>{let r=o.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&a){let[r]=e.split(a[0]);return u({getSafeRouteKey:c,interceptionMarker:r,segment:a[1],routeKeys:l,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return a?u({getSafeRouteKey:c,segment:a[1],routeKeys:l,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,i.escapeStringRegexp)(e)}).join(""),routeKeys:l}}function f(e,t){let r=d(e,t);return{...l(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function p(e,t){let{parameterizedRoute:r}=c(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=d(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},839:(e,t,r)=>{"use strict";r.d(t,{AlO:()=>u,E33:()=>f,EQ9:()=>m,Imn:()=>p,KCr:()=>y,NOg:()=>d,QqI:()=>I,Rgz:()=>o,TCC:()=>c,UgA:()=>s,WY8:()=>P,Wqx:()=>R,_hL:()=>l,_rq:()=>a,bfl:()=>b,cur:()=>g,i63:()=>h,kXG:()=>E,nae:()=>v,q5L:()=>x,vlc:()=>i,wOW:()=>_});var n=r(2094);function o(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}},{tag:"polyline",attr:{points:"12 5 19 12 12 19"}}]})(e)}function i(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"4",width:"18",height:"18",rx:"2",ry:"2"}},{tag:"line",attr:{x1:"16",y1:"2",x2:"16",y2:"6"}},{tag:"line",attr:{x1:"8",y1:"2",x2:"8",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"10",x2:"21",y2:"10"}}]})(e)}function a(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}},{tag:"polyline",attr:{points:"22 4 12 14.01 9 11.01"}}]})(e)}function s(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 6 9 17 4 12"}}]})(e)}function c(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"polyline",attr:{points:"12 6 12 12 16 14"}}]})(e)}function l(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"7 10 12 15 17 10"}},{tag:"line",attr:{x1:"12",y1:"15",x2:"12",y2:"3"}}]})(e)}function u(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}},{tag:"polyline",attr:{points:"15 3 21 3 21 9"}},{tag:"line",attr:{x1:"10",y1:"14",x2:"21",y2:"3"}}]})(e)}function d(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}},{tag:"polyline",attr:{points:"14 2 14 8 20 8"}},{tag:"line",attr:{x1:"16",y1:"13",x2:"8",y2:"13"}},{tag:"line",attr:{x1:"16",y1:"17",x2:"8",y2:"17"}},{tag:"polyline",attr:{points:"10 9 9 9 8 9"}}]})(e)}function f(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 12 20 22 4 22 4 12"}},{tag:"rect",attr:{x:"2",y:"7",width:"20",height:"5"}},{tag:"line",attr:{x1:"12",y1:"22",x2:"12",y2:"7"}},{tag:"path",attr:{d:"M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"}},{tag:"path",attr:{d:"M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"}}]})(e)}function p(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}},{tag:"polyline",attr:{points:"22,6 12,13 2,6"}}]})(e)}function h(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}},{tag:"circle",attr:{cx:"12",cy:"10",r:"3"}}]})(e)}function g(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"3",y1:"12",x2:"21",y2:"12"}},{tag:"line",attr:{x1:"3",y1:"6",x2:"21",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"18",x2:"21",y2:"18"}}]})(e)}function m(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"}}]})(e)}function _(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"}}]})(e)}function y(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"5 3 19 12 5 21 5 3"}}]})(e)}function b(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"}}]})(e)}function v(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"}}]})(e)}function E(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"5"}},{tag:"line",attr:{x1:"12",y1:"1",x2:"12",y2:"3"}},{tag:"line",attr:{x1:"12",y1:"21",x2:"12",y2:"23"}},{tag:"line",attr:{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}},{tag:"line",attr:{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}},{tag:"line",attr:{x1:"1",y1:"12",x2:"3",y2:"12"}},{tag:"line",attr:{x1:"21",y1:"12",x2:"23",y2:"12"}},{tag:"line",attr:{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}},{tag:"line",attr:{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"}}]})(e)}function I(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 6 13.5 15.5 8.5 10.5 1 18"}},{tag:"polyline",attr:{points:"17 6 23 6 23 12"}}]})(e)}function P(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"9",cy:"7",r:"4"}},{tag:"path",attr:{d:"M23 21v-2a4 4 0 0 0-3-3.87"}},{tag:"path",attr:{d:"M16 3.13a4 4 0 0 1 0 7.75"}}]})(e)}function x(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(e)}function R(e){return(0,n.GenIcon)({tag:"svg",attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polygon",attr:{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2"}}]})(e)}},3619:(e,t,r)=>{"use strict";var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};Object.defineProperty(t,"__esModule",{value:!0}),t.IconBase=t.GenIcon=void 0;var i=r(6689),a=r(6233);function s(e){var t=function(t){var r,a=e.attr,s=e.size,c=e.title,l=o(e,["attr","size","title"]),u=s||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),i.createElement("svg",n({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,l,{className:r,style:n(n({color:e.color||t.color},t.style),e.style),height:u,width:u,xmlns:"http://www.w3.org/2000/svg"}),c&&i.createElement("title",null,c),e.children)};return void 0!==a.IconContext?i.createElement(a.IconContext.Consumer,null,function(e){return t(e)}):t(a.DefaultContext)}t.GenIcon=function(e){return function(t){return i.createElement(s,n({attr:n({},e.attr)},t),function e(t){return t&&t.map(function(t,r){return i.createElement(t.tag,n({key:r},t.attr),e(t.child))})}(e.child))}},t.IconBase=s},6233:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IconContext=t.DefaultContext=void 0;var n=r(6689);t.DefaultContext={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},t.IconContext=n.createContext&&n.createContext(t.DefaultContext)},5215:e=>{"use strict";e.exports.IconsManifest=[{id:"ci",name:"Circum Icons",projectUrl:"https://circumicons.com/",license:"MPL-2.0 license",licenseUrl:"https://github.com/Klarr-Agency/Circum-Icons/blob/main/LICENSE"},{id:"fa",name:"Font Awesome 5",projectUrl:"https://fontawesome.com/",license:"CC BY 4.0 License",licenseUrl:"https://creativecommons.org/licenses/by/4.0/"},{id:"fa6",name:"Font Awesome 6",projectUrl:"https://fontawesome.com/",license:"CC BY 4.0 License",licenseUrl:"https://creativecommons.org/licenses/by/4.0/"},{id:"io",name:"Ionicons 4",projectUrl:"https://ionicons.com/",license:"MIT",licenseUrl:"https://github.com/ionic-team/ionicons/blob/master/LICENSE"},{id:"io5",name:"Ionicons 5",projectUrl:"https://ionicons.com/",license:"MIT",licenseUrl:"https://github.com/ionic-team/ionicons/blob/master/LICENSE"},{id:"md",name:"Material Design icons",projectUrl:"http://google.github.io/material-design-icons/",license:"Apache License Version 2.0",licenseUrl:"https://github.com/google/material-design-icons/blob/master/LICENSE"},{id:"ti",name:"Typicons",projectUrl:"http://s-ings.com/typicons/",license:"CC BY-SA 3.0",licenseUrl:"https://creativecommons.org/licenses/by-sa/3.0/"},{id:"go",name:"Github Octicons icons",projectUrl:"https://octicons.github.com/",license:"MIT",licenseUrl:"https://github.com/primer/octicons/blob/master/LICENSE"},{id:"fi",name:"Feather",projectUrl:"https://feathericons.com/",license:"MIT",licenseUrl:"https://github.com/feathericons/feather/blob/master/LICENSE"},{id:"lu",name:"Lucide",projectUrl:"https://lucide.dev/",license:"ISC",licenseUrl:"https://github.com/lucide-icons/lucide/blob/main/LICENSE"},{id:"gi",name:"Game Icons",projectUrl:"https://game-icons.net/",license:"CC BY 3.0",licenseUrl:"https://creativecommons.org/licenses/by/3.0/"},{id:"wi",name:"Weather Icons",projectUrl:"https://erikflowers.github.io/weather-icons/",license:"SIL OFL 1.1",licenseUrl:"http://scripts.sil.org/OFL"},{id:"di",name:"Devicons",projectUrl:"https://vorillaz.github.io/devicons/",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"ai",name:"Ant Design Icons",projectUrl:"https://github.com/ant-design/ant-design-icons",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"bs",name:"Bootstrap Icons",projectUrl:"https://github.com/twbs/icons",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"ri",name:"Remix Icon",projectUrl:"https://github.com/Remix-Design/RemixIcon",license:"Apache License Version 2.0",licenseUrl:"http://www.apache.org/licenses/"},{id:"fc",name:"Flat Color Icons",projectUrl:"https://github.com/icons8/flat-color-icons",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"gr",name:"Grommet-Icons",projectUrl:"https://github.com/grommet/grommet-icons",license:"Apache License Version 2.0",licenseUrl:"http://www.apache.org/licenses/"},{id:"hi",name:"Heroicons",projectUrl:"https://github.com/tailwindlabs/heroicons",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"hi2",name:"Heroicons 2",projectUrl:"https://github.com/tailwindlabs/heroicons",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"si",name:"Simple Icons",projectUrl:"https://simpleicons.org/",license:"CC0 1.0 Universal",licenseUrl:"https://creativecommons.org/publicdomain/zero/1.0/"},{id:"sl",name:"Simple Line Icons",projectUrl:"https://thesabbir.github.io/simple-line-icons/",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"im",name:"IcoMoon Free",projectUrl:"https://github.com/Keyamoon/IcoMoon-Free",license:"CC BY 4.0 License",licenseUrl:"https://github.com/Keyamoon/IcoMoon-Free/blob/master/License.txt"},{id:"bi",name:"BoxIcons",projectUrl:"https://github.com/atisawd/boxicons",license:"CC BY 4.0 License",licenseUrl:"https://github.com/atisawd/boxicons/blob/master/LICENSE"},{id:"cg",name:"css.gg",projectUrl:"https://github.com/astrit/css.gg",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"vsc",name:"VS Code Icons",projectUrl:"https://github.com/microsoft/vscode-codicons",license:"CC BY 4.0",licenseUrl:"https://creativecommons.org/licenses/by/4.0/"},{id:"tb",name:"Tabler Icons",projectUrl:"https://github.com/tabler/tabler-icons",license:"MIT",licenseUrl:"https://opensource.org/licenses/MIT"},{id:"tfi",name:"Themify Icons",projectUrl:"https://github.com/lykmapipo/themify-icons",license:"MIT",licenseUrl:"https://github.com/thecreation/standard-icons/blob/master/modules/themify-icons/LICENSE"},{id:"rx",name:"Radix Icons",projectUrl:"https://icons.radix-ui.com",license:"MIT",licenseUrl:"https://github.com/radix-ui/icons/blob/master/LICENSE"},{id:"pi",name:"Phosphor Icons",projectUrl:"https://github.com/phosphor-icons/core",license:"MIT",licenseUrl:"https://github.com/phosphor-icons/core/blob/main/LICENSE"},{id:"lia",name:"Icons8 Line Awesome",projectUrl:"https://icons8.com/line-awesome",license:"MIT",licenseUrl:"https://github.com/icons8/line-awesome/blob/master/LICENSE.md"}]},2094:(e,t,r)=>{"use strict";var n=Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]},o=function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),o(r(5215),t),o(r(3619),t),o(r(6233),t)},2350:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return c},APP_DIR_ALIAS:function(){return j},CACHE_ONE_YEAR:function(){return v},DOT_NEXT_ALIAS:function(){return R},ESLINT_DEFAULT_DIRS:function(){return V},GSP_NO_RETURNED_VALUE:function(){return D},GSSP_COMPONENT_MEMBER_ERROR:function(){return F},GSSP_NO_RETURNED_VALUE:function(){return W},INSTRUMENTATION_HOOK_FILENAME:function(){return P},MIDDLEWARE_FILENAME:function(){return E},MIDDLEWARE_LOCATION_REGEXP:function(){return I},NEXT_BODY_SUFFIX:function(){return d},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return b},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return h},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return g},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return p},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return y},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return m},NEXT_CACHE_TAG_MAX_LENGTH:function(){return _},NEXT_DATA_SUFFIX:function(){return l},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return u},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return H},PAGES_DIR_ALIAS:function(){return x},PRERENDER_REVALIDATE_HEADER:function(){return o},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return i},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return L},ROOT_DIR_ALIAS:function(){return O},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return M},RSC_ACTION_ENCRYPTION_ALIAS:function(){return S},RSC_ACTION_PROXY_ALIAS:function(){return A},RSC_ACTION_VALIDATE_ALIAS:function(){return T},RSC_MOD_REF_PROXY_ALIAS:function(){return C},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SUFFIX:function(){return s},SERVER_PROPS_EXPORT_ERROR:function(){return G},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return U},SERVER_PROPS_SSG_CONFLICT:function(){return N},SERVER_RUNTIME:function(){return Y},SSG_FALLBACK_EXPORT_ERROR:function(){return X},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return k},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return w},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return B},WEBPACK_LAYERS:function(){return K},WEBPACK_RESOURCE_QUERIES:function(){return q}});let r="nxtP",n="nxtI",o="x-prerender-revalidate",i="x-prerender-revalidate-if-generated",a=".prefetch.rsc",s=".rsc",c=".action",l=".json",u=".meta",d=".body",f="x-next-cache-tags",p="x-next-cache-soft-tags",h="x-next-revalidated-tags",g="x-next-revalidate-tag-token",m=128,_=256,y=1024,b="_N_T_",v=31536e3,E="middleware",I=`(?:src/)?${E}`,P="instrumentation",x="private-next-pages",R="private-dot-next",O="private-next-root-dir",j="private-next-app-dir",C="private-next-rsc-mod-ref-proxy",T="private-next-rsc-action-validate",A="private-next-rsc-server-reference",S="private-next-rsc-action-encryption",M="private-next-rsc-action-client-wrapper",L="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",k="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",U="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",N="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",w="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",G="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",D="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",W="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",B="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",F="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",H='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',X="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",V=["app","pages","components","lib","src"],Y={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},K={...z,GROUP:{serverOnly:[z.reactServerComponents,z.actionBrowser,z.appMetadataRoute,z.appRouteHandler,z.instrument],clientOnly:[z.serverSideRendering,z.appPagesBrowser],nonClientServerTarget:[z.middleware,z.api],app:[z.reactServerComponents,z.actionBrowser,z.appMetadataRoute,z.appRouteHandler,z.serverSideRendering,z.appPagesBrowser,z.shared,z.instrument]}},q={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},5244:(e,t)=>{"use strict";var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},7443:(e,t,r)=>{"use strict";e.exports=r(7093).vendored.contexts.AppRouterContext},5469:(e,t,r)=>{"use strict";e.exports=r(7093).vendored.contexts.RouterContext},1664:(e,t,r)=>{e.exports=r(9577)},8760:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=t._interop_require_wildcard=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=e[a]}return o.default=e,n&&n.set(e,o),o}}};