"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[395],{395:function(e,r,a){a.d(r,{Z:function(){return x}});var t=a(5893),s=a(7294),l=a(1664),i=a.n(l),n=a(2010),o=a(1526),c=a(3495),d=a(4052);function m(){let[e,r]=(0,s.useState)(!1),[a,l]=(0,s.useState)(!1),{theme:m,setTheme:h}=(0,n.F)();(0,s.useEffect)(()=>{l(!0)},[]);let x=[{href:"/",label:"Home"},{href:"/airdrop",label:"Airdrop"},{href:"/tokenomics",label:"Tokenomics"},{href:"/roadmap",label:"Roadmap"},{href:"/whitepaper",label:"Whitepaper"},{href:"/community",label:"Community"}];return(0,t.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-dark-900/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-700",children:[(0,t.jsx)("div",{className:"container-custom",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 group",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,t.jsx)("span",{className:"text-dark-900 font-bold text-lg",children:"H"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-dark-900 dark:text-white",children:"Hehe Miner"})]}),(0,t.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:x.map(e=>(0,t.jsx)(i(),{href:e.href,className:"text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300 font-medium",children:e.label},e.href))}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[a&&(0,t.jsx)("button",{onClick:()=>h("dark"===m?"light":"dark"),className:"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300","aria-label":"Toggle theme",children:"dark"===m?(0,t.jsx)(d.kXG,{size:20}):(0,t.jsx)(d.wOW,{size:20})}),(0,t.jsx)("a",{href:"https://t.me/HeheMinerBot",target:"_blank",rel:"noopener noreferrer",className:"btn btn-primary",children:"\uD83D\uDE80 Play Now"})]}),(0,t.jsxs)("div",{className:"md:hidden flex items-center space-x-2",children:[a&&(0,t.jsx)("button",{onClick:()=>h("dark"===m?"light":"dark"),className:"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300","aria-label":"Toggle theme",children:"dark"===m?(0,t.jsx)(d.kXG,{size:18}):(0,t.jsx)(d.wOW,{size:18})}),(0,t.jsx)("button",{onClick:()=>r(!e),className:"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300","aria-label":"Toggle menu",children:e?(0,t.jsx)(d.q5L,{size:20}):(0,t.jsx)(d.cur,{size:20})})]})]})}),(0,t.jsx)(o.M,{children:e&&(0,t.jsx)(c.E.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"md:hidden bg-white dark:bg-dark-900 border-t border-gray-200 dark:border-dark-700",children:(0,t.jsx)("div",{className:"container-custom py-4",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4",children:[x.map(e=>(0,t.jsx)(i(),{href:e.href,onClick:()=>r(!1),className:"text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300 font-medium py-2",children:e.label},e.href)),(0,t.jsx)("div",{className:"pt-4 border-t border-gray-200 dark:border-dark-700",children:(0,t.jsx)("a",{href:"https://t.me/HeheMinerBot",target:"_blank",rel:"noopener noreferrer",className:"btn btn-primary w-full justify-center",onClick:()=>r(!1),children:"\uD83D\uDE80 Play Now"})})]})})})})]})}function h(){let e=new Date().getFullYear();return(0,t.jsx)("footer",{className:"bg-dark-900 text-white",children:(0,t.jsxs)("div",{className:"container-custom",children:[(0,t.jsx)("div",{className:"py-16",children:(0,t.jsxs)("div",{className:"grid lg:grid-cols-5 gap-12",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-xl flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-dark-900 font-bold text-xl",children:"H"})}),(0,t.jsx)("span",{className:"text-2xl font-bold",children:"Hehe Miner"})]}),(0,t.jsx)("p",{className:"text-gray-400 mb-6 leading-relaxed max-w-md",children:"The ultimate Telegram mining game with real token rewards. Mine 4 HEHE tokens every 4 hours and participate in our massive 500M token airdrop."}),(0,t.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[(0,t.jsx)(d.i63,{className:"w-5 h-5 text-primary-400"}),(0,t.jsx)("span",{children:"London, United Kingdom"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 text-gray-400",children:[(0,t.jsx)(d.Imn,{className:"w-5 h-5 text-primary-400"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:space-x-4",children:[(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-primary-400 transition-colors",children:"<EMAIL>"}),(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:text-primary-400 transition-colors",children:"<EMAIL>"})]})]})]}),(0,t.jsx)("div",{className:"flex space-x-4",children:[{name:"Telegram Bot",href:"https://t.me/HeheMinerBot",icon:"\uD83E\uDD16",color:"hover:text-blue-400"},{name:"Community",href:"https://t.me/Hehe_miner_community",icon:"\uD83D\uDCAC",color:"hover:text-primary-400"},{name:"Twitter/X",href:"https://x.com/Hehe_Miner",icon:"\uD83D\uDC26",color:"hover:text-gray-400"},{name:"YouTube",href:"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ",icon:"\uD83D\uDCFA",color:"hover:text-red-400"}].map(e=>(0,t.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"w-12 h-12 bg-dark-800 rounded-lg flex items-center justify-center text-xl transition-all duration-300 hover:bg-dark-700 hover:scale-110 ".concat(e.color),"aria-label":e.name,children:e.icon},e.name))})]}),(0,t.jsxs)("div",{className:"lg:col-span-3 grid sm:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6 text-primary-400",children:"Game"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{label:"Play Now",href:"https://t.me/HeheMinerBot",external:!0},{label:"How to Play",href:"/how-to-play"},{label:"Airdrop",href:"/airdrop"},{label:"Tokenomics",href:"/tokenomics"}].map(e=>(0,t.jsx)("li",{children:e.external?(0,t.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group",children:[e.label,(0,t.jsx)(d.AlO,{className:"w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity"})]}):(0,t.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-300",children:e.label})},e.label))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6 text-primary-400",children:"Resources"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{label:"Whitepaper",href:"/whitepaper"},{label:"Roadmap",href:"/roadmap"},{label:"FAQ",href:"/faq"},{label:"Support",href:"/support"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-300",children:e.label})},e.label))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6 text-primary-400",children:"Community"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{label:"Telegram Bot",href:"https://t.me/HeheMinerBot",external:!0},{label:"Community Chat",href:"https://t.me/Hehe_miner_community",external:!0},{label:"Twitter/X",href:"https://x.com/Hehe_Miner",external:!0},{label:"YouTube",href:"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ",external:!0}].map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group",children:[e.label,(0,t.jsx)(d.AlO,{className:"w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity"})]})},e.label))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold mb-6 text-primary-400",children:"Legal"}),(0,t.jsx)("ul",{className:"space-y-3",children:[{label:"Terms of Service",href:"/terms"},{label:"Privacy Policy",href:"/privacy"},{label:"Cookie Policy",href:"/cookies"},{label:"Contact Us",href:"/contact"}].map(e=>(0,t.jsx)("li",{children:(0,t.jsx)(i(),{href:e.href,className:"text-gray-400 hover:text-white transition-colors duration-300",children:e.label})},e.label))})]})]})]})}),(0,t.jsx)("div",{className:"border-t border-dark-700 py-8",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,t.jsxs)("div",{className:"text-gray-400 text-center md:text-left",children:[(0,t.jsxs)("p",{children:["\xa9 ",e," Hehe Miner. All rights reserved."]}),(0,t.jsx)("p",{className:"text-sm mt-1",children:"Built with ❤️ in London, UK | Powered by Polygon Blockchain"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-400",children:[(0,t.jsxs)("span",{className:"flex items-center",children:["\uD83D\uDD12 ",(0,t.jsx)("span",{className:"ml-1",children:"Secure"})]}),(0,t.jsxs)("span",{className:"flex items-center",children:["⚡ ",(0,t.jsx)("span",{className:"ml-1",children:"Fast"})]}),(0,t.jsxs)("span",{className:"flex items-center",children:["\uD83C\uDF0D ",(0,t.jsx)("span",{className:"ml-1",children:"Global"})]})]})]})}),(0,t.jsx)("div",{className:"border-t border-dark-700 py-6",children:(0,t.jsxs)("div",{className:"text-xs text-gray-500 text-center max-w-4xl mx-auto leading-relaxed",children:[(0,t.jsxs)("p",{className:"mb-2",children:[(0,t.jsx)("strong",{children:"Disclaimer:"})," Hehe Miner is a gaming application. HEHE tokens are utility tokens for in-game activities. This is not financial advice. Cryptocurrency investments carry risk. Please do your own research before participating."]}),(0,t.jsx)("p",{children:"The information provided on this website is for educational and entertainment purposes only. Token distribution is subject to terms and conditions. Game mechanics and tokenomics may be updated based on community feedback and development requirements."})]})})]})})}function x(e){let{children:r}=e;return(0,t.jsxs)("div",{className:"min-h-screen bg-white dark:bg-dark-900 transition-colors duration-300",children:[(0,t.jsx)(m,{}),(0,t.jsx)("main",{className:"relative",children:r}),(0,t.jsx)(h,{})]})}}}]);