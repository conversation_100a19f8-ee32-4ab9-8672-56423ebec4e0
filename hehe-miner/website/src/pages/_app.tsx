import type { AppProps } from 'next/app'
import { ThemeProvider } from 'next-themes'
import Head from 'next/head'
import { useEffect } from 'react'
import '../styles/globals.css'

export default function App({ Component, pageProps }: AppProps) {
  useEffect(() => {
    // Add any global initialization here
    console.log('🎮 Hehe Miner Website Loaded!')
  }, [])

  return (
    <>
      <Head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#33ffff" />
        
        {/* Global Meta Tags */}
        <meta name="author" content="Hehe Miner Team" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="English" />
        <meta name="revisit-after" content="7 days" />
        
        {/* Open Graph Global */}
        <meta property="og:site_name" content="Hehe Miner" />
        <meta property="og:locale" content="en_US" />
        
        {/* Twitter Global */}
        <meta name="twitter:creator" content="@Hehe_Miner" />
        <meta name="twitter:site" content="@Hehe_Miner" />
        
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://t.me" />
        <link rel="preconnect" href="https://x.com" />
        
        {/* DNS Prefetch */}
        <link rel="dns-prefetch" href="//youtube.com" />
        <link rel="dns-prefetch" href="//telegram.org" />
        
        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "Hehe Miner",
              "description": "The ultimate Telegram mining game with real token rewards",
              "url": "https://hehe-miner.vercel.app",
              "applicationCategory": "Game",
              "operatingSystem": "Web, Telegram",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD"
              },
              "author": {
                "@type": "Organization",
                "name": "Hehe Miner Team",
                "email": "<EMAIL>"
              }
            })
          }}
        />
      </Head>
      
      <ThemeProvider 
        attribute="class" 
        defaultTheme="dark" 
        enableSystem={true}
        disableTransitionOnChange={false}
      >
        <Component {...pageProps} />
      </ThemeProvider>
    </>
  )
}
