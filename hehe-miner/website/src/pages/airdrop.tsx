import Head from 'next/head'
import Layout from '../components/Layout'
import { motion } from 'framer-motion'
import { FiGift, FiUsers, FiClock, FiCheckCircle, FiArrowRight } from 'react-icons/fi'

export default function Airdrop() {
  const airdropSteps = [
    {
      step: 1,
      title: 'Join the Game',
      description: 'Start playing Hehe Miner on Telegram and begin mining HEHE tokens',
      action: 'Play Now',
      link: 'https://t.me/HeheMinerBot',
      icon: '🎮',
      status: 'active'
    },
    {
      step: 2,
      title: 'Mine & Earn',
      description: 'Mine tokens every 4 hours, complete tasks, and refer friends',
      action: 'Keep Mining',
      link: 'https://t.me/HeheMinerBot',
      icon: '⛏️',
      status: 'active'
    },
    {
      step: 3,
      title: 'Snapshot',
      description: 'We will take a snapshot of all active miners before TGE',
      action: 'Stay Active',
      link: '#',
      icon: '📸',
      status: 'upcoming'
    },
    {
      step: 4,
      title: 'TGE & Claim',
      description: 'Claim your airdrop tokens after Token Generation Event',
      action: 'Claim Tokens',
      link: '#',
      icon: '🎁',
      status: 'upcoming'
    }
  ]

  const airdropStats = [
    { number: '350M', label: 'HEHE Tokens', sublabel: '70% of total supply' },
    { number: '0%', label: 'Vesting Period', sublabel: 'Instant distribution for community' },
    { number: '10K+', label: 'Eligible Miners', sublabel: 'And growing daily' },
    { number: 'Q2 2025', label: 'Expected TGE', sublabel: 'Token Generation Event' },
  ]

  const eligibilityCriteria = [
    {
      icon: '✅',
      title: 'Active Mining',
      description: 'Regularly mine HEHE tokens (at least once per week)',
      points: 'Base allocation'
    },
    {
      icon: '🎯',
      title: 'Task Completion',
      description: 'Complete social media tasks and community activities',
      points: 'Bonus multiplier'
    },
    {
      icon: '👥',
      title: 'Referrals',
      description: 'Refer friends to join the mining community',
      points: '+0.5 HEHE per referral'
    },
    {
      icon: '⚡',
      title: 'Speed Upgrades',
      description: 'Purchase mining speed upgrades with TON',
      points: 'Premium allocation'
    }
  ]

  return (
    <>
      <Head>
        <title>HEHE Token Airdrop - 350M Tokens for Community | Hehe Miner</title>
        <meta 
          name="description" 
          content="Join the massive HEHE token airdrop! 350M tokens (70% of supply) distributed to active miners. No vesting, instant distribution at TGE. Start mining now to qualify!" 
        />
        <meta 
          name="keywords" 
          content="HEHE airdrop, crypto airdrop, Telegram game airdrop, free tokens, mining airdrop, Polygon airdrop, TGE, token distribution" 
        />
        
        {/* Open Graph */}
        <meta property="og:title" content="HEHE Token Airdrop - 350M Tokens for Community" />
        <meta property="og:description" content="Join the massive HEHE token airdrop! 350M tokens distributed to active miners with no vesting period." />
        <meta property="og:image" content="/images/airdrop-og.png" />
        <meta property="og:url" content="https://hehe-miner.vercel.app/airdrop" />
        
        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="HEHE Token Airdrop - 350M Tokens for Community" />
        <meta name="twitter:description" content="Join the massive HEHE token airdrop! 350M tokens distributed to active miners with no vesting period." />
        <meta name="twitter:image" content="/images/airdrop-twitter.png" />
        
        <link rel="canonical" href="https://hehe-miner.vercel.app/airdrop" />
      </Head>

      <Layout>
        {/* Hero Section */}
        <section className="pt-24 pb-16 bg-gradient-to-br from-primary-400 via-primary-500 to-secondary-400 relative overflow-hidden">
          <div className="absolute inset-0 bg-grid-pattern opacity-20" />
          
          <div className="container-custom relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center"
            >
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl mb-8">
                <FiGift className="w-10 h-10 text-dark-900" />
              </div>
              
              <h1 className="text-4xl md:text-6xl font-bold text-dark-900 mb-6">
                HEHE Token Airdrop
              </h1>
              
              <p className="text-xl md:text-2xl text-dark-800 mb-8 max-w-3xl mx-auto">
                Join the largest Telegram gaming airdrop! 350 million HEHE tokens 
                (70% of total supply) distributed to our amazing community.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="https://t.me/HeheMinerBot"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn bg-dark-900 text-white hover:bg-dark-800 btn-large"
                >
                  🚀 Start Mining to Qualify
                </a>
                <a
                  href="https://t.me/Hehe_miner_community"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn bg-white/20 backdrop-blur-sm text-dark-900 hover:bg-white/30 btn-large border-2 border-dark-900/20"
                >
                  💬 Join Community
                </a>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Airdrop Stats */}
        <section className="section-padding bg-white dark:bg-dark-900">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16"
            >
              {airdropStats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2">
                    {stat.number}
                  </div>
                  <div className="text-lg font-semibold text-dark-900 dark:text-white mb-1">
                    {stat.label}
                  </div>
                  <div className="text-sm text-dark-600 dark:text-dark-300">
                    {stat.sublabel}
                  </div>
                </div>
              ))}
            </motion.div>
          </div>
        </section>

        {/* How to Qualify */}
        <section className="section-padding bg-gray-50 dark:bg-dark-800">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
                How to{' '}
                <span className="text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                  Qualify
                </span>
              </h2>
              <p className="text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto">
                Follow these simple steps to maximize your airdrop allocation
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {airdropSteps.map((step, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  <div className={`card p-8 text-center h-full ${
                    step.status === 'active' 
                      ? 'border-primary-400 shadow-glow' 
                      : 'border-gray-300 dark:border-dark-600'
                  }`}>
                    <div className={`w-16 h-16 mx-auto mb-6 rounded-2xl flex items-center justify-center text-2xl ${
                      step.status === 'active'
                        ? 'bg-primary-400/20 text-primary-400'
                        : 'bg-gray-400/20 text-gray-400'
                    }`}>
                      {step.icon}
                    </div>
                    
                    <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-4 ${
                      step.status === 'active'
                        ? 'bg-primary-400/20 text-primary-400'
                        : 'bg-gray-400/20 text-gray-400'
                    }`}>
                      Step {step.step}
                    </div>
                    
                    <h3 className="text-xl font-bold text-dark-900 dark:text-white mb-4">
                      {step.title}
                    </h3>
                    
                    <p className="text-dark-600 dark:text-dark-300 mb-6">
                      {step.description}
                    </p>
                    
                    {step.status === 'active' ? (
                      <a
                        href={step.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="btn btn-primary w-full"
                      >
                        {step.action}
                      </a>
                    ) : (
                      <button className="btn btn-outline w-full" disabled>
                        {step.action}
                      </button>
                    )}
                  </div>
                  
                  {index < airdropSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                      <FiArrowRight className="w-8 h-8 text-primary-400" />
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Eligibility Criteria */}
        <section className="section-padding bg-white dark:bg-dark-900">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
                Eligibility{' '}
                <span className="text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                  Criteria
                </span>
              </h2>
              <p className="text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto">
                Multiple ways to increase your airdrop allocation
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8">
              {eligibilityCriteria.map((criteria, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="card p-8 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl">{criteria.icon}</div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-dark-900 dark:text-white mb-3">
                        {criteria.title}
                      </h3>
                      <p className="text-dark-600 dark:text-dark-300 mb-4">
                        {criteria.description}
                      </p>
                      <div className="inline-block px-3 py-1 bg-primary-400/20 text-primary-400 rounded-full text-sm font-semibold">
                        {criteria.points}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Important Notes */}
        <section className="section-padding bg-gray-50 dark:bg-dark-800">
          <div className="container-custom">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="max-w-4xl mx-auto"
            >
              <div className="card p-8 border-l-4 border-primary-400">
                <h3 className="text-2xl font-bold text-dark-900 dark:text-white mb-6 flex items-center">
                  <FiCheckCircle className="w-8 h-8 text-primary-400 mr-3" />
                  Important Information
                </h3>
                
                <div className="space-y-4 text-dark-600 dark:text-dark-300">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-400 rounded-full mt-2 flex-shrink-0" />
                    <p><strong>No Vesting:</strong> All community airdrop tokens are distributed immediately at TGE with no vesting period.</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-400 rounded-full mt-2 flex-shrink-0" />
                    <p><strong>Snapshot Date:</strong> Will be announced in advance. Stay active until the snapshot to maximize allocation.</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-400 rounded-full mt-2 flex-shrink-0" />
                    <p><strong>Distribution Method:</strong> Claim directly from smart contract after TGE using your connected wallet.</p>
                  </div>
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-400 rounded-full mt-2 flex-shrink-0" />
                    <p><strong>Blockchain:</strong> HEHE tokens will be distributed on Polygon network for low-cost transactions.</p>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="section-padding bg-gradient-to-br from-primary-400 to-secondary-400">
          <div className="container-custom text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-dark-900 mb-6">
                Don't Miss Out!
              </h2>
              <p className="text-xl text-dark-800 mb-8 max-w-2xl mx-auto">
                Start mining now to secure your share of the 350M HEHE token airdrop. 
                The earlier you start, the larger your allocation!
              </p>
              <a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                className="btn bg-dark-900 text-white hover:bg-dark-800 btn-large"
              >
                🚀 Start Mining Now
              </a>
            </motion.div>
          </div>
        </section>
      </Layout>
    </>
  )
}
