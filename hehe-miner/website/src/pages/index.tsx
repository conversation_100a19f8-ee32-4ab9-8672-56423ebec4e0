import Head from 'next/head'
import Layout from '../components/Layout'
import Hero from '../components/Hero'
import Features from '../components/Features'
import Tokenomics from '../components/Tokenomics'
import Roadmap from '../components/Roadmap'
import Community from '../components/Community'
import CTA from '../components/CTA'

export default function Home() {
  return (
    <>
      <Head>
        <title>Hehe Miner - Telegram Mining Game & Airdrop | Mine HEHE Tokens</title>
        <meta 
          name="description" 
          content="Join Hehe Miner, the ultimate Telegram mining game! Mine 4 HEHE tokens every 4 hours, complete tasks, refer friends, and participate in our massive 500M token airdrop on Polygon blockchain." 
        />
        <meta 
          name="keywords" 
          content="Hehe Miner, Telegram game, crypto mining, airdrop, HEHE token, blockchain game, Polygon, mining game, referral rewards, free tokens" 
        />
        
        {/* Open Graph */}
        <meta property="og:title" content="Hehe Miner - Telegram Mining Game & Airdrop" />
        <meta property="og:description" content="Mine 4 HEHE tokens every 4 hours! Join the ultimate Telegram mining game with 500M token airdrop." />
        <meta property="og:image" content="/images/og-image.png" />
        <meta property="og:url" content="https://hehe-miner.vercel.app" />
        <meta property="og:type" content="website" />
        
        {/* Twitter Card */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Hehe Miner - Telegram Mining Game & Airdrop" />
        <meta name="twitter:description" content="Mine 4 HEHE tokens every 4 hours! Join the ultimate Telegram mining game with 500M token airdrop." />
        <meta name="twitter:image" content="/images/twitter-card.png" />
        
        {/* Additional SEO */}
        <link rel="canonical" href="https://hehe-miner.vercel.app" />
        <meta name="geo.region" content="GB-LND" />
        <meta name="geo.placename" content="London" />
        <meta name="geo.position" content="51.5074;-0.1278" />
        <meta name="ICBM" content="51.5074, -0.1278" />
        
        {/* Structured Data for Game */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "VideoGame",
              "name": "Hehe Miner",
              "description": "Telegram-based mining game where players can mine HEHE tokens every 4 hours",
              "url": "https://hehe-miner.vercel.app",
              "image": "/images/game-logo.png",
              "genre": "Mining Game",
              "gamePlatform": "Telegram",
              "applicationCategory": "Game",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
              },
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "1000"
              },
              "publisher": {
                "@type": "Organization",
                "name": "Hehe Miner Team",
                "address": {
                  "@type": "PostalAddress",
                  "addressLocality": "London",
                  "addressCountry": "UK"
                },
                "contactPoint": {
                  "@type": "ContactPoint",
                  "email": "<EMAIL>",
                  "contactType": "customer service"
                }
              }
            })
          }}
        />
      </Head>

      <Layout>
        <Hero />
        <Features />
        <Tokenomics />
        <Roadmap />
        <Community />
        <CTA />
      </Layout>
    </>
  )
}
