import { motion } from 'framer-motion'
import Link from 'next/link'
import { FiP<PERSON>, FiGift, FiUsers, FiClock } from 'react-icons/fi'

export default function Hero() {
  const stats = [
    { icon: FiGift, number: '500M', label: 'Total Airdrop', color: 'text-primary-400' },
    { icon: FiUsers, number: '70%', label: 'Community Share', color: 'text-secondary-400' },
    { icon: FiClock, number: '4 Hours', label: 'Mining Cycle', color: 'text-primary-400' },
  ]

  const floatingTokens = ['H', 'E', 'H', 'E']

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden hero-bg">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-400/10 via-transparent to-secondary-400/10" />
        
        {/* Animated Grid */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-grid-pattern animate-pulse-slow" />
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-primary-400/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>

      <div className="container-custom relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-left"
          >
            <motion.h1 
              className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Mine{' '}
              <span className="text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                HEHE Tokens
              </span>
              <br />
              Every 4 Hours!
            </motion.h1>

            <motion.p 
              className="text-xl text-gray-300 mb-8 max-w-2xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Join the ultimate Telegram mining game! Mine 4 HEHE tokens every 4 hours, 
              complete tasks, refer friends, and participate in our massive 500M token airdrop.
            </motion.p>

            {/* Stats */}
            <motion.div 
              className="grid grid-cols-3 gap-6 mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-white/10 backdrop-blur-sm mb-3 ${stat.color}`}>
                    <stat.icon size={24} />
                  </div>
                  <div className="text-2xl font-bold text-white">{stat.number}</div>
                  <div className="text-sm text-gray-400">{stat.label}</div>
                </div>
              ))}
            </motion.div>

            {/* Action Buttons */}
            <motion.div 
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <a
                href="https://t.me/HeheMinerBot"
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary btn-large group"
              >
                <FiPlay className="mr-2 group-hover:scale-110 transition-transform" />
                Start Mining Now
              </a>
              <Link href="/airdrop" className="btn btn-secondary btn-large group">
                <FiGift className="mr-2 group-hover:scale-110 transition-transform" />
                Join Airdrop
              </Link>
            </motion.div>
          </motion.div>

          {/* Right Visual */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative flex items-center justify-center"
          >
            {/* Phone Mockup */}
            <div className="relative">
              <motion.div
                className="w-80 h-96 bg-gradient-to-br from-dark-800 to-dark-900 rounded-3xl p-6 shadow-2xl border border-primary-400/20"
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                <div className="w-full h-full bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-2xl flex flex-col items-center justify-center relative overflow-hidden">
                  {/* Game UI Mockup */}
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full flex items-center justify-center mb-4 mx-auto glow-effect">
                      <span className="text-2xl font-bold text-dark-900">⛏️</span>
                    </div>
                    <div className="text-white font-bold text-xl mb-2">Mining Active</div>
                    <div className="text-primary-400 text-lg">4.0 HEHE</div>
                    <div className="text-gray-400 text-sm">Next mine in 3:45:12</div>
                  </div>

                  {/* Floating Tokens */}
                  {floatingTokens.map((token, index) => (
                    <motion.div
                      key={index}
                      className="absolute w-12 h-12 bg-secondary-400 rounded-full flex items-center justify-center text-dark-900 font-bold shadow-gold-glow"
                      style={{
                        left: `${20 + index * 15}%`,
                        top: `${20 + index * 10}%`,
                      }}
                      animate={{
                        y: [0, -20, 0],
                        rotate: [0, 360],
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: index * 0.5,
                      }}
                    >
                      {token}
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Floating Elements Around Phone */}
              <motion.div
                className="absolute -top-4 -right-4 w-16 h-16 bg-primary-400 rounded-full flex items-center justify-center text-dark-900 font-bold shadow-glow"
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              >
                💎
              </motion.div>

              <motion.div
                className="absolute -bottom-4 -left-4 w-12 h-12 bg-secondary-400 rounded-full flex items-center justify-center text-dark-900 font-bold shadow-gold-glow"
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                🚀
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary-400 rounded-full mt-2 animate-pulse" />
        </div>
      </motion.div>
    </section>
  )
}
