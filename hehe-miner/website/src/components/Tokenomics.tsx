import { motion } from 'framer-motion'
import { useEffect, useRef } from 'react'
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'
import { Doughnut } from 'react-chartjs-2'

ChartJS.register(ArcElement, Tooltip, Legend)

export default function Tokenomics() {
  const chartRef = useRef(null)

  const tokenData = {
    labels: [
      'Community Airdrop (70%)',
      'Liquidity Pool (10%)',
      'Partners & Ecosystem (10%)',
      'Team (5%)',
      'Treasury (5%)'
    ],
    datasets: [
      {
        data: [70, 10, 10, 5, 5],
        backgroundColor: [
          '#33ffff', // Primary cyan
          '#ffd700', // Gold
          '#ff6b6b', // Red
          '#4ecdc4', // Teal
          '#95e1d3'  // Light teal
        ],
        borderColor: [
          '#33ffff',
          '#ffd700',
          '#ff6b6b',
          '#4ecdc4',
          '#95e1d3'
        ],
        borderWidth: 2,
        hoverOffset: 10,
      },
    ],
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(15, 23, 42, 0.9)',
        titleColor: '#33ffff',
        bodyColor: '#ffffff',
        borderColor: '#33ffff',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.parsed}% (${(context.parsed * 5).toFixed(0)}M HEHE)`
          }
        }
      },
    },
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
    },
  }

  const allocations = [
    {
      label: 'Community Airdrop',
      percentage: '70%',
      amount: '350M HEHE',
      color: '#33ffff',
      description: 'No vesting - distributed immediately at TGE to community members',
      icon: '🎁'
    },
    {
      label: 'Liquidity Pool',
      percentage: '10%',
      amount: '50M HEHE',
      color: '#ffd700',
      description: 'No vesting - immediate DEX liquidity for trading and price stability',
      icon: '💧'
    },
    {
      label: 'Partners & Ecosystem',
      percentage: '10%',
      amount: '50M HEHE',
      color: '#ff6b6b',
      description: 'No vesting - immediate distribution for strategic partnerships',
      icon: '🤝'
    },
    {
      label: 'Team',
      percentage: '5%',
      amount: '25M HEHE',
      color: '#4ecdc4',
      description: '24 month linear vesting - only allocation with vesting period',
      icon: '👥'
    },
    {
      label: 'Treasury',
      percentage: '5%',
      amount: '25M HEHE',
      color: '#95e1d3',
      description: 'No vesting - immediate access for development and marketing',
      icon: '🏛️'
    },
  ]

  const tokenDetails = [
    { label: 'Total Supply', value: '500,000,000 HEHE' },
    { label: 'Blockchain', value: 'Polygon' },
    { label: 'Token Type', value: 'ERC-20' },
    { label: 'Decimals', value: '18' },
    { label: 'Initial Price', value: 'TBD at TGE' },
    { label: 'Listing', value: 'DEX First, CEX Later' },
  ]

  return (
    <section id="tokenomics" className="section-padding bg-white dark:bg-dark-900">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6">
            <span className="text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
              Tokenomics
            </span>
          </h2>
          <p className="text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto">
            Fair and transparent distribution of 500 million HEHE tokens with 
            community-first allocation and no vesting for airdrop participants.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Chart Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="w-full h-96 relative">
              <Doughnut ref={chartRef} data={tokenData} options={chartOptions} />
            </div>
            
            {/* Center Info */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <div className="text-3xl font-bold text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                  500M
                </div>
                <div className="text-dark-600 dark:text-dark-300 font-medium">
                  HEHE Tokens
                </div>
              </div>
            </div>
          </motion.div>

          {/* Details Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Token Details */}
            <div className="card p-6">
              <h3 className="text-2xl font-bold text-dark-900 dark:text-white mb-6">
                Token Details
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {tokenDetails.map((detail, index) => (
                  <div key={index} className="flex justify-between items-center py-2">
                    <span className="text-dark-600 dark:text-dark-300 font-medium">
                      {detail.label}:
                    </span>
                    <span className="text-dark-900 dark:text-white font-semibold">
                      {detail.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Allocation Breakdown */}
            <div className="card p-6">
              <h3 className="text-2xl font-bold text-dark-900 dark:text-white mb-6">
                Allocation Breakdown
              </h3>
              <div className="space-y-4">
                {allocations.map((allocation, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start space-x-4 p-4 rounded-lg bg-gray-50 dark:bg-dark-800 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
                  >
                    <div 
                      className="w-4 h-4 rounded-full mt-1 flex-shrink-0"
                      style={{ backgroundColor: allocation.color }}
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-semibold text-dark-900 dark:text-white">
                          {allocation.icon} {allocation.label}
                        </span>
                        <div className="text-right">
                          <div className="font-bold text-dark-900 dark:text-white">
                            {allocation.percentage}
                          </div>
                          <div className="text-sm text-dark-600 dark:text-dark-300">
                            {allocation.amount}
                          </div>
                        </div>
                      </div>
                      <p className="text-sm text-dark-600 dark:text-dark-300">
                        {allocation.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Key Features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 grid md:grid-cols-3 gap-8"
        >
          {[
            {
              icon: '🚫',
              title: 'No Vesting for 95%',
              description: '95% of tokens distributed immediately - only team has vesting'
            },
            {
              icon: '🔒',
              title: 'Team Vesting Only',
              description: '24-month linear vesting for team ensures long-term commitment'
            },
            {
              icon: '💧',
              title: 'Instant Everything Else',
              description: 'Community, liquidity, partners, treasury - all immediate at TGE'
            },
          ].map((feature, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h4 className="text-lg font-bold text-dark-900 dark:text-white mb-2">
                {feature.title}
              </h4>
              <p className="text-dark-600 dark:text-dark-300">
                {feature.description}
              </p>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
