@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');

:root {
  --primary-color: #33ffff;
  --secondary-color: #ffd700;
  --background-dark: #0f172a;
  --background-light: #ffffff;
  --text-dark: #1e293b;
  --text-light: #f8fafc;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background: var(--background-light);
  overflow-x: hidden;
}

.dark body {
  color: var(--text-light);
  background: var(--background-dark);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #33ffff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1affff;
}

/* Custom components */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-400 text-dark-900 hover:bg-primary-300 focus:ring-primary-400 shadow-glow hover:shadow-glow-lg;
  }

  .btn-secondary {
    @apply bg-secondary-400 text-dark-900 hover:bg-secondary-300 focus:ring-secondary-400 shadow-gold-glow;
  }

  .btn-outline {
    @apply border-2 border-primary-400 text-primary-400 hover:bg-primary-400 hover:text-dark-900 focus:ring-primary-400;
  }

  .btn-large {
    @apply px-8 py-4 text-base;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent;
  }

  .card {
    @apply bg-white dark:bg-dark-800 rounded-xl shadow-lg border border-gray-200 dark:border-dark-700 transition-all duration-300;
  }

  .card-hover {
    @apply hover:shadow-xl hover:scale-105 hover:border-primary-400/50;
  }

  .section-padding {
    @apply py-16 md:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 via-secondary-400 to-primary-400 bg-clip-text text-transparent;
  }

  .hero-bg {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }

  .dark .hero-bg {
    background: linear-gradient(135deg, #000000 0%, #0f172a 50%, #1e293b 100%);
  }

  .floating-animation {
    animation: float 6s ease-in-out infinite;
  }

  .glow-effect {
    box-shadow: 0 0 20px rgba(51, 255, 255, 0.3);
  }

  .dark .glow-effect {
    box-shadow: 0 0 30px rgba(51, 255, 255, 0.4);
  }
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(51, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(51, 255, 255, 0.6);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Loading states */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-dark-700 rounded;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }
  
  .section-padding {
    @apply py-12;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-primary-400 ring-offset-2 ring-offset-white dark:ring-offset-dark-900;
}

/* Custom selection */
::selection {
  background-color: rgba(51, 255, 255, 0.3);
  color: inherit;
}

.dark ::selection {
  background-color: rgba(51, 255, 255, 0.4);
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}
