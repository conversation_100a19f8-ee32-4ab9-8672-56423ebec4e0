/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "__barrel_optimize__?names=FiCalendar,FiCheck,FiClock!=!./node_modules/react-icons/fi/index.esm.js":
/*!*********************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiCalendar,FiCheck,FiClock!=!./node_modules/react-icons/fi/index.esm.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiClock,FiGift,FiPlay,FiUsers!=!./node_modules/react-icons/fi/index.esm.js":
/*!************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiClock,FiGift,FiPlay,FiUsers!=!./node_modules/react-icons/fi/index.esm.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!./node_modules/react-icons/fi/index.esm.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!./node_modules/react-icons/fi/index.esm.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiExternalLink,FiMessageCircle,FiPlay,FiUsers!=!./node_modules/react-icons/fi/index.esm.js":
/*!****************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiExternalLink,FiMessageCircle,FiPlay,FiUsers!=!./node_modules/react-icons/fi/index.esm.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiGift,FiPlay,FiTrendingUp,FiUsers!=!./node_modules/react-icons/fi/index.esm.js":
/*!*****************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiGift,FiPlay,FiTrendingUp,FiUsers!=!./node_modules/react-icons/fi/index.esm.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiGift,FiShield,FiStar,FiTrendingUp,FiUsers,FiZap!=!./node_modules/react-icons/fi/index.esm.js":
/*!********************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiGift,FiShield,FiStar,FiTrendingUp,FiUsers,FiZap!=!./node_modules/react-icons/fi/index.esm.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!./node_modules/react-icons/fi/index.esm.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!./node_modules/react-icons/fi/index.esm.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/index.tsx */ \"./src/pages/index.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/CTA.tsx":
/*!********************************!*\
  !*** ./src/components/CTA.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTA)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiGift,FiPlay,FiTrendingUp,FiUsers!=!react-icons/fi */ \"__barrel_optimize__?names=FiGift,FiPlay,FiTrendingUp,FiUsers!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction CTA() {\n    const benefits = [\n        {\n            icon: _barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlay,\n            text: \"Start mining in seconds\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiGift,\n            text: \"Free to begin\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiTrendingUp,\n            text: \"Earn real tokens\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiUsers,\n            text: \"Join 15K+ miners\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gradient-to-br from-primary-400 via-primary-500 to-secondary-400 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-grid-pattern animate-pulse-slow\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(15)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                className: \"absolute w-4 h-4 bg-white/20 rounded-full\",\n                                style: {\n                                    left: `${Math.random() * 100}%`,\n                                    top: `${Math.random() * 100}%`\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -40,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.2,\n                                        0.6,\n                                        0.2\n                                    ],\n                                    scale: [\n                                        1,\n                                        1.2,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 4 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 2\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        className: \"absolute -top-20 -right-20 w-40 h-40 bg-white/10 rounded-full\",\n                        animate: {\n                            rotate: 360\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                        className: \"absolute -bottom-20 -left-20 w-60 h-60 bg-white/5 rounded-full\",\n                        animate: {\n                            rotate: -360\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h2, {\n                            className: \"text-4xl md:text-6xl font-bold text-dark-900 mb-6\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: \"Ready to Start Mining?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                            className: \"text-xl md:text-2xl text-dark-800 mb-8 max-w-3xl mx-auto\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: \"Join thousands of miners earning HEHE tokens every day! Start your crypto mining journey in the most rewarding Telegram game.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\",\n                            children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.8 + index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"flex flex-col items-center text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(benefit.icon, {\n                                                className: \"w-8 h-8 text-dark-900\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-dark-900 font-semibold\",\n                                            children: benefit.text\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                                    href: \"https://t.me/HeheMinerBot\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn bg-dark-900 text-white hover:bg-dark-800 btn-large group shadow-2xl\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlay, {\n                                            className: \"mr-3 group-hover:scale-110 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"\\uD83D\\uDE80 Start Mining Now\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    className: \"flex items-center space-x-4\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/airdrop\",\n                                        className: \"btn bg-white/20 backdrop-blur-sm text-dark-900 hover:bg-white/30 btn-large border-2 border-dark-900/20 group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiGift_FiPlay_FiTrendingUp_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiGift, {\n                                                className: \"mr-3 group-hover:scale-110 transition-transform\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"\\uD83C\\uDF81 Join Airdrop\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            whileInView: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1.2\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mt-12 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-dark-800 text-lg mb-4\",\n                                    children: [\n                                        \"\\uD83C\\uDFAF \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"500M HEHE tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 18\n                                        }, this),\n                                        \" to be distributed to the community\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center items-center gap-6 text-dark-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                \"✅ \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"ml-2\",\n                                                    children: \"Free to start\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                \"⚡ \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"ml-2\",\n                                                    children: \"Instant rewards\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                \"\\uD83D\\uDD12 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"ml-2\",\n                                                    children: \"Secure & transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 20\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                \"\\uD83C\\uDF0D \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    className: \"ml-2\",\n                                                    children: \"Global community\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 20\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 1.4\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"mt-8 inline-block\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/20 backdrop-blur-sm rounded-full px-6 py-3 border border-dark-900/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-dark-900 font-semibold\",\n                                    children: \"⏰ Join now and be part of the TGE snapshot!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/CTA.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CTA.tsx\n");

/***/ }),

/***/ "./src/components/Community.tsx":
/*!**************************************!*\
  !*** ./src/components/Community.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Community)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FiExternalLink_FiMessageCircle_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiExternalLink,FiMessageCircle,FiPlay,FiUsers!=!react-icons/fi */ \"__barrel_optimize__?names=FiExternalLink,FiMessageCircle,FiPlay,FiUsers!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Community() {\n    const communityLinks = [\n        {\n            name: \"Telegram Bot\",\n            description: \"Start mining HEHE tokens right now\",\n            icon: \"\\uD83E\\uDD16\",\n            url: \"https://t.me/HeheMinerBot\",\n            members: \"10K+ Miners\",\n            color: \"from-blue-400 to-blue-600\",\n            hoverColor: \"hover:shadow-blue-400/30\"\n        },\n        {\n            name: \"Community Chat\",\n            description: \"Join discussions with fellow miners\",\n            icon: \"\\uD83D\\uDCAC\",\n            url: \"https://t.me/Hehe_miner_community\",\n            members: \"5K+ Members\",\n            color: \"from-primary-400 to-primary-600\",\n            hoverColor: \"hover:shadow-primary-400/30\"\n        },\n        {\n            name: \"Twitter/X\",\n            description: \"Latest updates, news & announcements\",\n            icon: \"\\uD83D\\uDC26\",\n            url: \"https://x.com/Hehe_Miner\",\n            members: \"2K+ Followers\",\n            color: \"from-gray-700 to-gray-900\",\n            hoverColor: \"hover:shadow-gray-400/30\"\n        },\n        {\n            name: \"YouTube Channel\",\n            description: \"Tutorials, gameplay & educational content\",\n            icon: \"\\uD83D\\uDCFA\",\n            url: \"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ\",\n            members: \"1K+ Subscribers\",\n            color: \"from-red-500 to-red-700\",\n            hoverColor: \"hover:shadow-red-400/30\"\n        }\n    ];\n    const stats = [\n        {\n            number: \"15K+\",\n            label: \"Total Community Members\"\n        },\n        {\n            number: \"50+\",\n            label: \"Countries Represented\"\n        },\n        {\n            number: \"24/7\",\n            label: \"Community Support\"\n        },\n        {\n            number: \"100%\",\n            label: \"Transparency\"\n        }\n    ];\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"community\",\n        className: \"section-padding bg-white dark:bg-dark-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6\",\n                            children: [\n                                \"Join Our\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                    children: \"Community\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto\",\n                            children: \"Connect with thousands of miners worldwide, share strategies, get support, and stay updated with the latest developments.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2\",\n                                    children: stat.number\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-dark-600 dark:text-dark-300 font-medium\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                    children: communityLinks.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.a, {\n                            variants: itemVariants,\n                            href: link.url,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: `group block card card-hover p-8 text-center transition-all duration-300 ${link.hoverColor} hover:scale-105`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-20 h-20 mx-auto mb-6 rounded-2xl bg-gradient-to-br ${link.color} flex items-center justify-center text-3xl group-hover:scale-110 transition-transform duration-300`,\n                                    children: link.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-dark-900 dark:text-white mb-3 group-hover:text-primary-400 transition-colors\",\n                                    children: link.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-dark-600 dark:text-dark-300 mb-4 leading-relaxed\",\n                                    children: link.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 text-primary-400 font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMessageCircle_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUsers, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: link.members\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-center text-primary-400 group-hover:text-secondary-400 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: \"Join Now\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMessageCircle_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiExternalLink, {\n                                            className: \"w-4 h-4 group-hover:translate-x-1 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-3 gap-8\",\n                    children: [\n                        {\n                            icon: _barrel_optimize_names_FiExternalLink_FiMessageCircle_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMessageCircle,\n                            title: \"Active Discussions\",\n                            description: \"Engage in daily conversations about mining strategies, token updates, and community events.\",\n                            color: \"text-blue-400\",\n                            bgColor: \"bg-blue-400/10\"\n                        },\n                        {\n                            icon: _barrel_optimize_names_FiExternalLink_FiMessageCircle_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUsers,\n                            title: \"Helpful Community\",\n                            description: \"Get help from experienced miners and support newcomers in their mining journey.\",\n                            color: \"text-green-400\",\n                            bgColor: \"bg-green-400/10\"\n                        },\n                        {\n                            icon: _barrel_optimize_names_FiExternalLink_FiMessageCircle_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiPlay,\n                            title: \"Exclusive Content\",\n                            description: \"Access exclusive tutorials, early announcements, and special community-only events.\",\n                            color: \"text-purple-400\",\n                            bgColor: \"bg-purple-400/10\"\n                        }\n                    ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `inline-flex items-center justify-center w-16 h-16 rounded-xl ${feature.bgColor} mb-6`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: `w-8 h-8 ${feature.color}`\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-dark-900 dark:text-white mb-4\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-dark-600 dark:text-dark-300 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8 max-w-2xl mx-auto bg-gradient-to-br from-primary-400/10 to-secondary-400/10 border border-primary-400/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-dark-900 dark:text-white mb-4\",\n                                children: \"Ready to Join the Mining Revolution?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-dark-600 dark:text-dark-300 mb-6\",\n                                children: \"Start your mining journey today and become part of the most active and supportive crypto gaming community!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://t.me/HeheMinerBot\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"btn btn-primary btn-large\",\n                                        children: \"\\uD83D\\uDE80 Start Mining\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://t.me/Hehe_miner_community\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"btn btn-secondary btn-large\",\n                                        children: \"\\uD83D\\uDCAC Join Chat\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Community.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Community.tsx\n");

/***/ }),

/***/ "./src/components/Features.tsx":
/*!*************************************!*\
  !*** ./src/components/Features.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiGift,FiShield,FiStar,FiTrendingUp,FiUsers,FiZap!=!react-icons/fi */ \"__barrel_optimize__?names=FiGift,FiShield,FiStar,FiTrendingUp,FiUsers,FiZap!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Features() {\n    const features = [\n        {\n            icon: _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiZap,\n            title: \"Easy Mining\",\n            description: \"Mine 4 HEHE tokens every 4 hours with just one tap. No complex setups or technical knowledge required!\",\n            color: \"text-primary-400\",\n            bgColor: \"bg-primary-400/10\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiGift,\n            title: \"Free to Start\",\n            description: \"Activate your basic mining plan completely free. No payment required to start earning HEHE tokens!\",\n            color: \"text-secondary-400\",\n            bgColor: \"bg-secondary-400/10\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiTrendingUp,\n            title: \"Speed Upgrades\",\n            description: \"Boost your mining power with TON-powered speed upgrades for faster earnings and higher rewards!\",\n            color: \"text-green-400\",\n            bgColor: \"bg-green-400/10\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUsers,\n            title: \"Referral Rewards\",\n            description: \"Earn 0.5 HEHE tokens for each friend you refer. Unlimited referrals with instant rewards!\",\n            color: \"text-purple-400\",\n            bgColor: \"bg-purple-400/10\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiStar,\n            title: \"Task System\",\n            description: \"Complete social media tasks and earn bonus HEHE tokens for engagement and community participation!\",\n            color: \"text-pink-400\",\n            bgColor: \"bg-pink-400/10\"\n        },\n        {\n            icon: _barrel_optimize_names_FiGift_FiShield_FiStar_FiTrendingUp_FiUsers_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiShield,\n            title: \"Blockchain Ready\",\n            description: \"Built on Polygon for low-cost, fast transactions when tokens go live. Secure and transparent!\",\n            color: \"text-blue-400\",\n            bgColor: \"bg-blue-400/10\"\n        }\n    ];\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30\n        },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.6\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gray-50 dark:bg-dark-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6\",\n                            children: [\n                                \"Why Choose\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                    children: \"Hehe Miner?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto\",\n                            children: \"Experience the most rewarding Telegram mining game with real token rewards, innovative features, and a thriving community.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            variants: itemVariants,\n                            className: \"group\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card card-hover p-8 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `inline-flex items-center justify-center w-16 h-16 rounded-xl ${feature.bgColor} mb-6 group-hover:scale-110 transition-transform duration-300`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                            className: `w-8 h-8 ${feature.color}`\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-dark-900 dark:text-white mb-4\",\n                                        children: feature.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-dark-600 dark:text-dark-300 leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-20 grid grid-cols-2 md:grid-cols-4 gap-8\",\n                    children: [\n                        {\n                            number: \"10K+\",\n                            label: \"Active Miners\"\n                        },\n                        {\n                            number: \"500M\",\n                            label: \"Total Tokens\"\n                        },\n                        {\n                            number: \"0.5\",\n                            label: \"Referral Reward\"\n                        },\n                        {\n                            number: \"4H\",\n                            label: \"Mining Cycle\"\n                        }\n                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-2\",\n                                    children: stat.number\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-dark-600 dark:text-dark-300 font-medium\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Features.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Features.tsx\n");

/***/ }),

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!react-icons/fi */ \"__barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!./node_modules/react-icons/fi/index.esm.js\");\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        game: [\n            {\n                label: \"Play Now\",\n                href: \"https://t.me/HeheMinerBot\",\n                external: true\n            },\n            {\n                label: \"How to Play\",\n                href: \"/how-to-play\"\n            },\n            {\n                label: \"Airdrop\",\n                href: \"/airdrop\"\n            },\n            {\n                label: \"Tokenomics\",\n                href: \"/tokenomics\"\n            }\n        ],\n        resources: [\n            {\n                label: \"Whitepaper\",\n                href: \"/whitepaper\"\n            },\n            {\n                label: \"Roadmap\",\n                href: \"/roadmap\"\n            },\n            {\n                label: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                label: \"Support\",\n                href: \"/support\"\n            }\n        ],\n        community: [\n            {\n                label: \"Telegram Bot\",\n                href: \"https://t.me/HeheMinerBot\",\n                external: true\n            },\n            {\n                label: \"Community Chat\",\n                href: \"https://t.me/Hehe_miner_community\",\n                external: true\n            },\n            {\n                label: \"Twitter/X\",\n                href: \"https://x.com/Hehe_Miner\",\n                external: true\n            },\n            {\n                label: \"YouTube\",\n                href: \"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ\",\n                external: true\n            }\n        ],\n        legal: [\n            {\n                label: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                label: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                label: \"Cookie Policy\",\n                href: \"/cookies\"\n            },\n            {\n                label: \"Contact Us\",\n                href: \"/contact\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Telegram Bot\",\n            href: \"https://t.me/HeheMinerBot\",\n            icon: \"\\uD83E\\uDD16\",\n            color: \"hover:text-blue-400\"\n        },\n        {\n            name: \"Community\",\n            href: \"https://t.me/Hehe_miner_community\",\n            icon: \"\\uD83D\\uDCAC\",\n            color: \"hover:text-primary-400\"\n        },\n        {\n            name: \"Twitter/X\",\n            href: \"https://x.com/Hehe_Miner\",\n            icon: \"\\uD83D\\uDC26\",\n            color: \"hover:text-gray-400\"\n        },\n        {\n            name: \"YouTube\",\n            href: \"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ\",\n            icon: \"\\uD83D\\uDCFA\",\n            color: \"hover:text-red-400\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-dark-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-5 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-dark-900 font-bold text-xl\",\n                                                    children: \"H\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"Hehe Miner\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-6 leading-relaxed max-w-md\",\n                                        children: \"The ultimate Telegram mining game with real token rewards. Mine 4 HEHE tokens every 4 hours and participate in our massive 500M token airdrop.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMapPin, {\n                                                        className: \"w-5 h-5 text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"London, United Kingdom\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMail, {\n                                                        className: \"w-5 h-5 text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"hover:text-primary-400 transition-colors\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"hover:text-primary-400 transition-colors\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: social.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: `w-12 h-12 bg-dark-800 rounded-lg flex items-center justify-center text-xl transition-all duration-300 hover:bg-dark-700 hover:scale-110 ${social.color}`,\n                                                \"aria-label\": social.name,\n                                                children: social.icon\n                                            }, social.name, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3 grid sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Game\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.game.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: link.external ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: link.href,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group\",\n                                                            children: [\n                                                                link.label,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiExternalLink, {\n                                                                    className: \"w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Resources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.resources.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Community\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.community.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: link.href,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group\",\n                                                            children: [\n                                                                link.label,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiExternalLink, {\n                                                                    className: \"w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Legal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-dark-700 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-center md:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" Hehe Miner. All rights reserved.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-1\",\n                                        children: \"Built with ❤️ in London, UK | Powered by Polygon Blockchain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"\\uD83D\\uDD12 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: \"Secure\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 20\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"⚡ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: \"Fast\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"\\uD83C\\uDF0D \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: \"Global\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 20\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-dark-700 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 text-center max-w-4xl mx-auto leading-relaxed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Disclaimer:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Hehe Miner is a gaming application. HEHE tokens are utility tokens for in-game activities. This is not financial advice. Cryptocurrency investments carry risk. Please do your own research before participating.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"The information provided on this website is for educational and entertainment purposes only. Token distribution is subject to terms and conditions. Game mechanics and tokenomics may be updated based on community feedback and development requirements.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n");

/***/ }),

/***/ "./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiClock_FiGift_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiClock,FiGift,FiPlay,FiUsers!=!react-icons/fi */ \"__barrel_optimize__?names=FiClock,FiGift,FiPlay,FiUsers!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction Hero() {\n    const stats = [\n        {\n            icon: _barrel_optimize_names_FiClock_FiGift_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiGift,\n            number: \"500M\",\n            label: \"Total Airdrop\",\n            color: \"text-primary-400\"\n        },\n        {\n            icon: _barrel_optimize_names_FiClock_FiGift_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiUsers,\n            number: \"70%\",\n            label: \"Community Share\",\n            color: \"text-secondary-400\"\n        },\n        {\n            icon: _barrel_optimize_names_FiClock_FiGift_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiClock,\n            number: \"4 Hours\",\n            label: \"Mining Cycle\",\n            color: \"text-primary-400\"\n        }\n    ];\n    const floatingTokens = [\n        \"H\",\n        \"E\",\n        \"H\",\n        \"E\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden hero-bg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-primary-400/10 via-transparent to-secondary-400/10\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-grid-pattern animate-pulse-slow\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(20)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                className: \"absolute w-2 h-2 bg-primary-400/30 rounded-full\",\n                                style: {\n                                    left: `${Math.random() * 100}%`,\n                                    top: `${Math.random() * 100}%`\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -30,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.3,\n                                        0.8,\n                                        0.3\n                                    ]\n                                },\n                                transition: {\n                                    duration: 3 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 2\n                                }\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"text-center lg:text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.h1, {\n                                    className: \"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2\n                                    },\n                                    children: [\n                                        \"Mine\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                            children: \"HEHE Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Every 4 Hours!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.p, {\n                                    className: \"text-xl text-gray-300 mb-8 max-w-2xl\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    children: \"Join the ultimate Telegram mining game! Mine 4 HEHE tokens every 4 hours, complete tasks, refer friends, and participate in our massive 500M token airdrop.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    className: \"grid grid-cols-3 gap-6 mb-8\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.6\n                                    },\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `inline-flex items-center justify-center w-12 h-12 rounded-lg bg-white/10 backdrop-blur-sm mb-3 ${stat.color}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-400\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.8\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://t.me/HeheMinerBot\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"btn btn-primary btn-large group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiGift_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlay, {\n                                                    className: \"mr-2 group-hover:scale-110 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Start Mining Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/airdrop\",\n                                            className: \"btn btn-secondary btn-large group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiClock_FiGift_FiPlay_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiGift, {\n                                                    className: \"mr-2 group-hover:scale-110 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Join Airdrop\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"relative flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                        className: \"w-80 h-96 bg-gradient-to-br from-dark-800 to-dark-900 rounded-3xl p-6 shadow-2xl border border-primary-400/20\",\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -10,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 4,\n                                            repeat: Infinity\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-2xl flex flex-col items-center justify-center relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-20 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full flex items-center justify-center mb-4 mx-auto glow-effect\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold text-dark-900\",\n                                                                children: \"⛏️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                                lineNumber: 142,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-white font-bold text-xl mb-2\",\n                                                            children: \"Mining Active\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-primary-400 text-lg\",\n                                                            children: \"4.0 HEHE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Next mine in 3:45:12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this),\n                                                floatingTokens.map((token, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                        className: \"absolute w-12 h-12 bg-secondary-400 rounded-full flex items-center justify-center text-dark-900 font-bold shadow-gold-glow\",\n                                                        style: {\n                                                            left: `${20 + index * 15}%`,\n                                                            top: `${20 + index * 10}%`\n                                                        },\n                                                        animate: {\n                                                            y: [\n                                                                0,\n                                                                -20,\n                                                                0\n                                                            ],\n                                                            rotate: [\n                                                                0,\n                                                                360\n                                                            ],\n                                                            scale: [\n                                                                1,\n                                                                1.1,\n                                                                1\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 3,\n                                                            repeat: Infinity,\n                                                            delay: index * 0.5\n                                                        },\n                                                        children: token\n                                                    }, index, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                        className: \"absolute -top-4 -right-4 w-16 h-16 bg-primary-400 rounded-full flex items-center justify-center text-dark-900 font-bold shadow-glow\",\n                                        animate: {\n                                            rotate: 360\n                                        },\n                                        transition: {\n                                            duration: 8,\n                                            repeat: Infinity,\n                                            ease: \"linear\"\n                                        },\n                                        children: \"\\uD83D\\uDC8E\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                        className: \"absolute -bottom-4 -left-4 w-12 h-12 bg-secondary-400 rounded-full flex items-center justify-center text-dark-900 font-bold shadow-gold-glow\",\n                                        animate: {\n                                            y: [\n                                                0,\n                                                -10,\n                                                0\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        children: \"\\uD83D\\uDE80\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                animate: {\n                    y: [\n                        0,\n                        10,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 2,\n                    repeat: Infinity\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-1 h-3 bg-primary-400 rounded-full mt-2 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Hero.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Hero.tsx\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navbar */ \"./src/components/Navbar.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"./src/components/Footer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Navbar__WEBPACK_IMPORTED_MODULE_1__]);\n_Navbar__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-dark-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUM2QjtBQUNBO0FBTWQsU0FBU0UsT0FBTyxFQUFFQyxRQUFRLEVBQWU7SUFDdEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCwrQ0FBTUE7Ozs7OzBCQUNQLDhEQUFDTTtnQkFBS0QsV0FBVTswQkFDYkY7Ozs7OzswQkFFSCw4REFBQ0YsK0NBQU1BOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVoZS1taW5lci13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvTGF5b3V0LnRzeD9kZThiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IE5hdmJhciBmcm9tICcuL05hdmJhcidcbmltcG9ydCBGb290ZXIgZnJvbSAnLi9Gb290ZXInXG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfTogTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZSBkYXJrOmJnLWRhcmstOTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgPE5hdmJhciAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTmF2YmFyIiwiRm9vdGVyIiwiTGF5b3V0IiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!react-icons/fi */ \"__barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_4__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction Navbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const toggleMenu = ()=>setIsOpen(!isOpen);\n    const navItems = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/airdrop\",\n            label: \"Airdrop\"\n        },\n        {\n            href: \"/tokenomics\",\n            label: \"Tokenomics\"\n        },\n        {\n            href: \"/roadmap\",\n            label: \"Roadmap\"\n        },\n        {\n            href: \"/whitepaper\",\n            label: \"Whitepaper\"\n        },\n        {\n            href: \"/community\",\n            label: \"Community\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-dark-900/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-dark-900 font-bold text-lg\",\n                                        children: \"H\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-dark-900 dark:text-white\",\n                                    children: \"Hehe Miner\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300 font-medium\",\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSun, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMoon, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 59\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://t.me/HeheMinerBot\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn btn-primary\",\n                                    children: \"\\uD83D\\uDE80 Play Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-2\",\n                            children: [\n                                mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSun, {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMoon, {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 59\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300\",\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 25\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMenu, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"md:hidden bg-white dark:bg-dark-900 border-t border-gray-200 dark:border-dark-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300 font-medium py-2\",\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-gray-200 dark:border-dark-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://t.me/HeheMinerBot\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"btn btn-primary w-full justify-center\",\n                                        onClick: ()=>setIsOpen(false),\n                                        children: \"\\uD83D\\uDE80 Play Now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.tsx\n");

/***/ }),

/***/ "./src/components/Roadmap.tsx":
/*!************************************!*\
  !*** ./src/components/Roadmap.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Roadmap)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FiCalendar_FiCheck_FiClock_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiCalendar,FiCheck,FiClock!=!react-icons/fi */ \"__barrel_optimize__?names=FiCalendar,FiCheck,FiClock!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Roadmap() {\n    const roadmapItems = [\n        {\n            quarter: \"Q3 2024\",\n            title: \"Game Launch & Foundation\",\n            status: \"completed\",\n            items: [\n                \"Telegram bot development and deployment\",\n                \"Basic mining mechanics implementation\",\n                \"Task system and social media integration\",\n                \"Referral program launch\",\n                \"Community building and user acquisition\"\n            ]\n        },\n        {\n            quarter: \"Q4 2024\",\n            title: \"Growth & Optimization\",\n            status: \"completed\",\n            items: [\n                \"Game mechanics refinement\",\n                \"Community expansion and engagement\",\n                \"Social media task system enhancement\",\n                \"Speed upgrade features with TON integration\",\n                \"User experience improvements\"\n            ]\n        },\n        {\n            quarter: \"Q1 2025\",\n            title: \"Token Development & Testing\",\n            status: \"active\",\n            items: [\n                \"Smart contract development on Polygon\",\n                \"Testnet deployment and testing\",\n                \"Security audits and code reviews\",\n                \"Whitepaper publication\",\n                \"Pre-TGE marketing campaign\"\n            ]\n        },\n        {\n            quarter: \"Q2 2025\",\n            title: \"Pre-Launch Preparation\",\n            status: \"upcoming\",\n            items: [\n                \"Final security audits completion\",\n                \"Community snapshot preparation\",\n                \"Exchange partnership negotiations\",\n                \"Marketing campaign intensification\",\n                \"Legal compliance finalization\"\n            ]\n        },\n        {\n            quarter: \"Q3 2025\",\n            title: \"Pre-TGE Activities\",\n            status: \"upcoming\",\n            items: [\n                \"Community snapshot execution\",\n                \"Airdrop allocation calculations\",\n                \"Final testing and preparations\",\n                \"Exchange listing confirmations\",\n                \"Launch countdown campaigns\"\n            ]\n        },\n        {\n            quarter: \"Q4 2025\",\n            title: \"TGE Preparation\",\n            status: \"upcoming\",\n            items: [\n                \"Smart contract deployment to mainnet\",\n                \"Final security checks and audits\",\n                \"Community education and preparation\",\n                \"Exchange integration testing\",\n                \"Launch infrastructure setup\"\n            ]\n        },\n        {\n            quarter: \"Q1 2026\",\n            title: \"TGE & Token Launch\",\n            status: \"upcoming\",\n            items: [\n                \"Token Generation Event (TGE)\",\n                \"Massive airdrop distribution to community\",\n                \"DEX listings (QuickSwap, SushiSwap)\",\n                \"Liquidity provision and trading launch\",\n                \"CEX listings on major exchanges\"\n            ]\n        },\n        {\n            quarter: \"Q2 2026+\",\n            title: \"Ecosystem Expansion\",\n            status: \"future\",\n            items: [\n                \"Advanced game features and mechanics\",\n                \"NFT integration and marketplace\",\n                \"Cross-chain bridge implementation\",\n                \"Staking and yield farming features\",\n                \"Global expansion and partnerships\"\n            ]\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiCheck_FiClock_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCheck, {\n                    className: \"w-6 h-6 text-green-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 16\n                }, this);\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiCheck_FiClock_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                    className: \"w-6 h-6 text-primary-400 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiCalendar_FiCheck_FiClock_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiCalendar, {\n                    className: \"w-6 h-6 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"border-green-400 bg-green-400/10\";\n            case \"active\":\n                return \"border-primary-400 bg-primary-400/10 shadow-glow\";\n            default:\n                return \"border-gray-400 bg-gray-400/10\";\n        }\n    };\n    const getTextColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"text-green-400\";\n            case \"active\":\n                return \"text-primary-400\";\n            default:\n                return \"text-gray-400\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"roadmap\",\n        className: \"section-padding bg-gray-50 dark:bg-dark-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                children: \"Roadmap\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto\",\n                            children: \"Our journey to revolutionize Telegram gaming and create the most rewarding mining experience in the crypto space.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-400 via-secondary-400 to-primary-400 transform md:-translate-x-1/2\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-12\",\n                            children: roadmapItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 50\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: `relative flex items-center ${index % 2 === 0 ? \"md:flex-row\" : \"md:flex-row-reverse\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `absolute left-8 md:left-1/2 w-16 h-16 rounded-full border-4 ${getStatusColor(item.status)} flex items-center justify-center transform md:-translate-x-1/2 z-10`,\n                                            children: getStatusIcon(item.status)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-full md:w-5/12 ml-24 md:ml-0 ${index % 2 === 0 ? \"md:mr-auto md:pr-8\" : \"md:ml-auto md:pl-8\"}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `inline-block px-4 py-2 rounded-full text-sm font-semibold mb-4 ${item.status === \"completed\" ? \"bg-green-400/20 text-green-400\" : item.status === \"active\" ? \"bg-primary-400/20 text-primary-400\" : \"bg-gray-400/20 text-gray-400\"}`,\n                                                        children: item.quarter\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-dark-900 dark:text-white mb-4\",\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-3\",\n                                                        children: item.items.map((subItem, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: `w-2 h-2 rounded-full mt-2 flex-shrink-0 ${item.status === \"completed\" ? \"bg-green-400\" : item.status === \"active\" ? \"bg-primary-400\" : \"bg-gray-400\"}`\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-dark-600 dark:text-dark-300 leading-relaxed\",\n                                                                        children: subItem\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                                        lineNumber: 208,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, subIndex, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.6\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card p-8 max-w-2xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-dark-900 dark:text-white mb-4\",\n                                children: \"\\uD83D\\uDE80 Current Focus: Token Development\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-dark-600 dark:text-dark-300 mb-6\",\n                                children: \"We're currently in Q1 2025, focusing on smart contract development, security audits, and preparing for our Token Generation Event (TGE) in Q1 2026.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://t.me/HeheMinerBot\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"btn btn-primary\",\n                                        children: \"\\uD83C\\uDFAE Start Mining Now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://t.me/Hehe_miner_community\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"btn btn-outline\",\n                                        children: \"\\uD83D\\uDCAC Join Community\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Roadmap.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Roadmap.tsx\n");

/***/ }),

/***/ "./src/components/Tokenomics.tsx":
/*!***************************************!*\
  !*** ./src/components/Tokenomics.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Tokenomics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! chart.js */ \"chart.js\");\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-chartjs-2 */ \"react-chartjs-2\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_1__, chart_js__WEBPACK_IMPORTED_MODULE_3__, react_chartjs_2__WEBPACK_IMPORTED_MODULE_4__]);\n([framer_motion__WEBPACK_IMPORTED_MODULE_1__, chart_js__WEBPACK_IMPORTED_MODULE_3__, react_chartjs_2__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nchart_js__WEBPACK_IMPORTED_MODULE_3__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_3__.ArcElement, chart_js__WEBPACK_IMPORTED_MODULE_3__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_3__.Legend);\nfunction Tokenomics() {\n    const chartRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const tokenData = {\n        labels: [\n            \"Community Airdrop (70%)\",\n            \"Liquidity Pool (10%)\",\n            \"Partners & Ecosystem (10%)\",\n            \"Team (5%)\",\n            \"Treasury (5%)\"\n        ],\n        datasets: [\n            {\n                data: [\n                    70,\n                    10,\n                    10,\n                    5,\n                    5\n                ],\n                backgroundColor: [\n                    \"#33ffff\",\n                    \"#ffd700\",\n                    \"#ff6b6b\",\n                    \"#4ecdc4\",\n                    \"#95e1d3\" // Light teal\n                ],\n                borderColor: [\n                    \"#33ffff\",\n                    \"#ffd700\",\n                    \"#ff6b6b\",\n                    \"#4ecdc4\",\n                    \"#95e1d3\"\n                ],\n                borderWidth: 2,\n                hoverOffset: 10\n            }\n        ]\n    };\n    const chartOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n            legend: {\n                display: false\n            },\n            tooltip: {\n                backgroundColor: \"rgba(15, 23, 42, 0.9)\",\n                titleColor: \"#33ffff\",\n                bodyColor: \"#ffffff\",\n                borderColor: \"#33ffff\",\n                borderWidth: 1,\n                cornerRadius: 8,\n                displayColors: true,\n                callbacks: {\n                    label: function(context) {\n                        return `${context.label}: ${context.parsed}% (${(context.parsed * 5).toFixed(0)}M HEHE)`;\n                    }\n                }\n            }\n        },\n        animation: {\n            animateRotate: true,\n            animateScale: true,\n            duration: 2000\n        }\n    };\n    const allocations = [\n        {\n            label: \"Community Airdrop\",\n            percentage: \"70%\",\n            amount: \"350M HEHE\",\n            color: \"#33ffff\",\n            description: \"No vesting - distributed immediately at TGE to community members\",\n            icon: \"\\uD83C\\uDF81\"\n        },\n        {\n            label: \"Liquidity Pool\",\n            percentage: \"10%\",\n            amount: \"50M HEHE\",\n            color: \"#ffd700\",\n            description: \"No vesting - immediate DEX liquidity for trading and price stability\",\n            icon: \"\\uD83D\\uDCA7\"\n        },\n        {\n            label: \"Partners & Ecosystem\",\n            percentage: \"10%\",\n            amount: \"50M HEHE\",\n            color: \"#ff6b6b\",\n            description: \"No vesting - immediate distribution for strategic partnerships\",\n            icon: \"\\uD83E\\uDD1D\"\n        },\n        {\n            label: \"Team\",\n            percentage: \"5%\",\n            amount: \"25M HEHE\",\n            color: \"#4ecdc4\",\n            description: \"24 month linear vesting - only allocation with vesting period\",\n            icon: \"\\uD83D\\uDC65\"\n        },\n        {\n            label: \"Treasury\",\n            percentage: \"5%\",\n            amount: \"25M HEHE\",\n            color: \"#95e1d3\",\n            description: \"No vesting - immediate access for development and marketing\",\n            icon: \"\\uD83C\\uDFDB️\"\n        }\n    ];\n    const tokenDetails = [\n        {\n            label: \"Total Supply\",\n            value: \"500,000,000 HEHE\"\n        },\n        {\n            label: \"Blockchain\",\n            value: \"Polygon\"\n        },\n        {\n            label: \"Token Type\",\n            value: \"ERC-20\"\n        },\n        {\n            label: \"Decimals\",\n            value: \"18\"\n        },\n        {\n            label: \"Initial Price\",\n            value: \"TBD at TGE\"\n        },\n        {\n            label: \"Listing\",\n            value: \"DEX First, CEX Later\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"tokenomics\",\n        className: \"section-padding bg-white dark:bg-dark-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-dark-900 dark:text-white mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                children: \"Tokenomics\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-dark-600 dark:text-dark-300 max-w-3xl mx-auto\",\n                            children: \"Fair and transparent distribution of 500 million HEHE tokens with community-first allocation and no vesting for airdrop participants.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-16 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-96 relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_4__.Doughnut, {\n                                        ref: chartRef,\n                                        data: tokenData,\n                                        options: chartOptions\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                                children: \"500M\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-dark-600 dark:text-dark-300 font-medium\",\n                                                children: \"HEHE Tokens\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-dark-900 dark:text-white mb-6\",\n                                            children: \"Token Details\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                            children: tokenDetails.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center py-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-dark-600 dark:text-dark-300 font-medium\",\n                                                            children: [\n                                                                detail.label,\n                                                                \":\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-dark-900 dark:text-white font-semibold\",\n                                                            children: detail.value\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-dark-900 dark:text-white mb-6\",\n                                            children: \"Allocation Breakdown\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: allocations.map((allocation, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        x: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        x: 0\n                                                    },\n                                                    transition: {\n                                                        duration: 0.5,\n                                                        delay: index * 0.1\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"flex items-start space-x-4 p-4 rounded-lg bg-gray-50 dark:bg-dark-800 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-4 h-4 rounded-full mt-1 flex-shrink-0\",\n                                                            style: {\n                                                                backgroundColor: allocation.color\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                            children: [\n                                                                                allocation.icon,\n                                                                                \" \",\n                                                                                allocation.label\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"font-bold text-dark-900 dark:text-white\",\n                                                                                    children: allocation.percentage\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                                                    lineNumber: 222,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                    children: allocation.amount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                                                    lineNumber: 225,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                    children: allocation.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 grid md:grid-cols-3 gap-8\",\n                    children: [\n                        {\n                            icon: \"\\uD83D\\uDEAB\",\n                            title: \"No Vesting for 95%\",\n                            description: \"95% of tokens distributed immediately - only team has vesting\"\n                        },\n                        {\n                            icon: \"\\uD83D\\uDD12\",\n                            title: \"Team Vesting Only\",\n                            description: \"24-month linear vesting for team ensures long-term commitment\"\n                        },\n                        {\n                            icon: \"\\uD83D\\uDCA7\",\n                            title: \"Instant Everything Else\",\n                            description: \"Community, liquidity, partners, treasury - all immediate at TGE\"\n                        }\n                    ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: feature.icon\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-bold text-dark-900 dark:text-white mb-2\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-dark-600 dark:text-dark-300\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Tokenomics.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Tokenomics.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Add any global initialization here\n        console.log(\"\\uD83C\\uDFAE Hehe Miner Website Loaded!\");\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#33ffff\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"author\",\n                        content: \"Hehe Miner Team\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index, follow\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"language\",\n                        content: \"English\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"revisit-after\",\n                        content: \"7 days\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"Hehe Miner\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:locale\",\n                        content: \"en_US\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:creator\",\n                        content: \"@Hehe_Miner\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:site\",\n                        content: \"@Hehe_Miner\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://t.me\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://x.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//youtube.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//telegram.org\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                \"name\": \"Hehe Miner\",\n                                \"description\": \"The ultimate Telegram mining game with real token rewards\",\n                                \"url\": \"https://hehe-miner.vercel.app\",\n                                \"applicationCategory\": \"Game\",\n                                \"operatingSystem\": \"Web, Telegram\",\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": \"0\",\n                                    \"priceCurrency\": \"USD\"\n                                },\n                                \"author\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Hehe Miner Team\",\n                                    \"email\": \"<EMAIL>\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                disableTransitionOnChange: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Hero */ \"./src/components/Hero.tsx\");\n/* harmony import */ var _components_Features__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Features */ \"./src/components/Features.tsx\");\n/* harmony import */ var _components_Tokenomics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Tokenomics */ \"./src/components/Tokenomics.tsx\");\n/* harmony import */ var _components_Roadmap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Roadmap */ \"./src/components/Roadmap.tsx\");\n/* harmony import */ var _components_Community__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/Community */ \"./src/components/Community.tsx\");\n/* harmony import */ var _components_CTA__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/CTA */ \"./src/components/CTA.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, _components_Hero__WEBPACK_IMPORTED_MODULE_3__, _components_Features__WEBPACK_IMPORTED_MODULE_4__, _components_Tokenomics__WEBPACK_IMPORTED_MODULE_5__, _components_Roadmap__WEBPACK_IMPORTED_MODULE_6__, _components_Community__WEBPACK_IMPORTED_MODULE_7__, _components_CTA__WEBPACK_IMPORTED_MODULE_8__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, _components_Hero__WEBPACK_IMPORTED_MODULE_3__, _components_Features__WEBPACK_IMPORTED_MODULE_4__, _components_Tokenomics__WEBPACK_IMPORTED_MODULE_5__, _components_Roadmap__WEBPACK_IMPORTED_MODULE_6__, _components_Community__WEBPACK_IMPORTED_MODULE_7__, _components_CTA__WEBPACK_IMPORTED_MODULE_8__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Hehe Miner - Telegram Mining Game & Airdrop | Mine HEHE Tokens\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Join Hehe Miner, the ultimate Telegram mining game! Mine 4 HEHE tokens every 4 hours, complete tasks, refer friends, and participate in our massive 500M token airdrop on Polygon blockchain.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"Hehe Miner, Telegram game, crypto mining, airdrop, HEHE token, blockchain game, Polygon, mining game, referral rewards, free tokens\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"Hehe Miner - Telegram Mining Game & Airdrop\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: \"Mine 4 HEHE tokens every 4 hours! Join the ultimate Telegram mining game with 500M token airdrop.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:image\",\n                        content: \"/images/og-image.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:url\",\n                        content: \"https://hehe-miner.vercel.app\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:type\",\n                        content: \"website\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:card\",\n                        content: \"summary_large_image\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:title\",\n                        content: \"Hehe Miner - Telegram Mining Game & Airdrop\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:description\",\n                        content: \"Mine 4 HEHE tokens every 4 hours! Join the ultimate Telegram mining game with 500M token airdrop.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:image\",\n                        content: \"/images/twitter-card.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://hehe-miner.vercel.app\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.region\",\n                        content: \"GB-LND\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.placename\",\n                        content: \"London\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"geo.position\",\n                        content: \"51.5074;-0.1278\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"ICBM\",\n                        content: \"51.5074, -0.1278\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"VideoGame\",\n                                \"name\": \"Hehe Miner\",\n                                \"description\": \"Telegram-based mining game where players can mine HEHE tokens every 4 hours\",\n                                \"url\": \"https://hehe-miner.vercel.app\",\n                                \"image\": \"/images/game-logo.png\",\n                                \"genre\": \"Mining Game\",\n                                \"gamePlatform\": \"Telegram\",\n                                \"applicationCategory\": \"Game\",\n                                \"operatingSystem\": \"Web\",\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": \"0\",\n                                    \"priceCurrency\": \"USD\",\n                                    \"availability\": \"https://schema.org/InStock\"\n                                },\n                                \"aggregateRating\": {\n                                    \"@type\": \"AggregateRating\",\n                                    \"ratingValue\": \"4.8\",\n                                    \"ratingCount\": \"1000\"\n                                },\n                                \"publisher\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Hehe Miner Team\",\n                                    \"address\": {\n                                        \"@type\": \"PostalAddress\",\n                                        \"addressLocality\": \"London\",\n                                        \"addressCountry\": \"UK\"\n                                    },\n                                    \"contactPoint\": {\n                                        \"@type\": \"ContactPoint\",\n                                        \"email\": \"<EMAIL>\",\n                                        \"contactType\": \"customer service\"\n                                    }\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Features__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Tokenomics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Roadmap__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Community__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTA__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/index.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "chart.js":
/*!***************************!*\
  !*** external "chart.js" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("chart.js");;

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ }),

/***/ "react-chartjs-2":
/*!**********************************!*\
  !*** external "react-chartjs-2" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-chartjs-2");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();