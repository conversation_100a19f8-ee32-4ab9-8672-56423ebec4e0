/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/whitepaper";
exports.ids = ["pages/whitepaper"];
exports.modules = {

/***/ "__barrel_optimize__?names=FiDownload,FiExternalLink,FiFileText!=!./node_modules/react-icons/fi/index.esm.js":
/*!*******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiDownload,FiExternalLink,FiFileText!=!./node_modules/react-icons/fi/index.esm.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!./node_modules/react-icons/fi/index.esm.js":
/*!*************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!./node_modules/react-icons/fi/index.esm.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "__barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!./node_modules/react-icons/fi/index.esm.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!./node_modules/react-icons/fi/index.esm.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-icons/fi/index.esm.js */ "./node_modules/react-icons/fi/index.esm.js");
/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};
/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== "default") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _Users_tahafarooqui_Documents_augment_projects_Hehe_coin_hehe_miner_website_node_modules_react_icons_fi_index_esm_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]
/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);


/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwhitepaper&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fwhitepaper.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwhitepaper&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fwhitepaper.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/whitepaper.tsx */ \"./src/pages/whitepaper.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/whitepaper\",\n        pathname: \"/whitepaper\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_whitepaper_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwhitepaper&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fwhitepaper.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!react-icons/fi */ \"__barrel_optimize__?names=FiExternalLink,FiMail,FiMapPin!=!./node_modules/react-icons/fi/index.esm.js\");\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        game: [\n            {\n                label: \"Play Now\",\n                href: \"https://t.me/HeheMinerBot\",\n                external: true\n            },\n            {\n                label: \"How to Play\",\n                href: \"/how-to-play\"\n            },\n            {\n                label: \"Airdrop\",\n                href: \"/airdrop\"\n            },\n            {\n                label: \"Tokenomics\",\n                href: \"/tokenomics\"\n            }\n        ],\n        resources: [\n            {\n                label: \"Whitepaper\",\n                href: \"/whitepaper\"\n            },\n            {\n                label: \"Roadmap\",\n                href: \"/roadmap\"\n            },\n            {\n                label: \"FAQ\",\n                href: \"/faq\"\n            },\n            {\n                label: \"Support\",\n                href: \"/support\"\n            }\n        ],\n        community: [\n            {\n                label: \"Telegram Bot\",\n                href: \"https://t.me/HeheMinerBot\",\n                external: true\n            },\n            {\n                label: \"Community Chat\",\n                href: \"https://t.me/Hehe_miner_community\",\n                external: true\n            },\n            {\n                label: \"Twitter/X\",\n                href: \"https://x.com/Hehe_Miner\",\n                external: true\n            },\n            {\n                label: \"YouTube\",\n                href: \"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ\",\n                external: true\n            }\n        ],\n        legal: [\n            {\n                label: \"Terms of Service\",\n                href: \"/terms\"\n            },\n            {\n                label: \"Privacy Policy\",\n                href: \"/privacy\"\n            },\n            {\n                label: \"Cookie Policy\",\n                href: \"/cookies\"\n            },\n            {\n                label: \"Contact Us\",\n                href: \"/contact\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Telegram Bot\",\n            href: \"https://t.me/HeheMinerBot\",\n            icon: \"\\uD83E\\uDD16\",\n            color: \"hover:text-blue-400\"\n        },\n        {\n            name: \"Community\",\n            href: \"https://t.me/Hehe_miner_community\",\n            icon: \"\\uD83D\\uDCAC\",\n            color: \"hover:text-primary-400\"\n        },\n        {\n            name: \"Twitter/X\",\n            href: \"https://x.com/Hehe_Miner\",\n            icon: \"\\uD83D\\uDC26\",\n            color: \"hover:text-gray-400\"\n        },\n        {\n            name: \"YouTube\",\n            href: \"https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ\",\n            icon: \"\\uD83D\\uDCFA\",\n            color: \"hover:text-red-400\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-dark-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-5 gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-dark-900 font-bold text-xl\",\n                                                    children: \"H\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: \"Hehe Miner\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-6 leading-relaxed max-w-md\",\n                                        children: \"The ultimate Telegram mining game with real token rewards. Mine 4 HEHE tokens every 4 hours and participate in our massive 500M token airdrop.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMapPin, {\n                                                        className: \"w-5 h-5 text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"London, United Kingdom\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMail, {\n                                                        className: \"w-5 h-5 text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col sm:flex-row sm:space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"hover:text-primary-400 transition-colors\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: \"mailto:<EMAIL>\",\n                                                                className: \"hover:text-primary-400 transition-colors\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: social.href,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: `w-12 h-12 bg-dark-800 rounded-lg flex items-center justify-center text-xl transition-all duration-300 hover:bg-dark-700 hover:scale-110 ${social.color}`,\n                                                \"aria-label\": social.name,\n                                                children: social.icon\n                                            }, social.name, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-3 grid sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Game\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.game.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: link.external ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: link.href,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group\",\n                                                            children: [\n                                                                link.label,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiExternalLink, {\n                                                                    className: \"w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                    lineNumber: 140,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Resources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.resources.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Community\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.community.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: link.href,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300 flex items-center group\",\n                                                            children: [\n                                                                link.label,\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiExternalLink_FiMail_FiMapPin_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiExternalLink, {\n                                                                    className: \"w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold mb-6 text-primary-400\",\n                                                children: \"Legal\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                            href: link.href,\n                                                            className: \"text-gray-400 hover:text-white transition-colors duration-300\",\n                                                            children: link.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, link.label, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-dark-700 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-center md:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" Hehe Miner. All rights reserved.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-1\",\n                                        children: \"Built with ❤️ in London, UK | Powered by Polygon Blockchain\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"\\uD83D\\uDD12 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: \"Secure\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 20\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"⚡ \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: \"Fast\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            \"\\uD83C\\uDF0D \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: \"Global\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 20\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-dark-700 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-500 text-center max-w-4xl mx-auto leading-relaxed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Disclaimer:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Hehe Miner is a gaming application. HEHE tokens are utility tokens for in-game activities. This is not financial advice. Cryptocurrency investments carry risk. Please do your own research before participating.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"The information provided on this website is for educational and entertainment purposes only. Token distribution is subject to terms and conditions. Game mechanics and tokenomics may be updated based on community feedback and development requirements.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Footer.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Footer.tsx\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Layout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navbar */ \"./src/components/Navbar.tsx\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Footer */ \"./src/components/Footer.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Navbar__WEBPACK_IMPORTED_MODULE_1__]);\n_Navbar__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nfunction Layout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white dark:bg-dark-900 transition-colors duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative\",\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUM2QjtBQUNBO0FBTWQsU0FBU0UsT0FBTyxFQUFFQyxRQUFRLEVBQWU7SUFDdEQscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDTCwrQ0FBTUE7Ozs7OzBCQUNQLDhEQUFDTTtnQkFBS0QsV0FBVTswQkFDYkY7Ozs7OzswQkFFSCw4REFBQ0YsK0NBQU1BOzs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaGVoZS1taW5lci13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvTGF5b3V0LnRzeD9kZThiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IE5hdmJhciBmcm9tICcuL05hdmJhcidcbmltcG9ydCBGb290ZXIgZnJvbSAnLi9Gb290ZXInXG5cbmludGVyZmFjZSBMYXlvdXRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGF5b3V0KHsgY2hpbGRyZW4gfTogTGF5b3V0UHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZSBkYXJrOmJnLWRhcmstOTAwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTMwMFwiPlxuICAgICAgPE5hdmJhciAvPlxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9tYWluPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiTmF2YmFyIiwiRm9vdGVyIiwiTGF5b3V0IiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!react-icons/fi */ \"__barrel_optimize__?names=FiMenu,FiMoon,FiSun,FiX!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([framer_motion__WEBPACK_IMPORTED_MODULE_4__]);\nframer_motion__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction Navbar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    const toggleMenu = ()=>setIsOpen(!isOpen);\n    const navItems = [\n        {\n            href: \"/\",\n            label: \"Home\"\n        },\n        {\n            href: \"/airdrop\",\n            label: \"Airdrop\"\n        },\n        {\n            href: \"/tokenomics\",\n            label: \"Tokenomics\"\n        },\n        {\n            href: \"/roadmap\",\n            label: \"Roadmap\"\n        },\n        {\n            href: \"/whitepaper\",\n            label: \"Whitepaper\"\n        },\n        {\n            href: \"/community\",\n            label: \"Community\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-dark-900/80 backdrop-blur-md border-b border-gray-200 dark:border-dark-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-dark-900 font-bold text-lg\",\n                                        children: \"H\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-dark-900 dark:text-white\",\n                                    children: \"Hehe Miner\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300 font-medium\",\n                                    children: item.label\n                                }, item.href, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: [\n                                mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSun, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMoon, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 59\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://t.me/HeheMinerBot\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"btn btn-primary\",\n                                    children: \"\\uD83D\\uDE80 Play Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden flex items-center space-x-2\",\n                            children: [\n                                mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    className: \"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300\",\n                                    \"aria-label\": \"Toggle theme\",\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiSun, {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 37\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMoon, {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 59\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMenu,\n                                    className: \"p-2 rounded-lg bg-gray-100 dark:bg-dark-800 text-dark-600 dark:text-dark-300\",\n                                    \"aria-label\": \"Toggle menu\",\n                                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 25\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiMenu_FiMoon_FiSun_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiMenu, {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 45\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    className: \"md:hidden bg-white dark:bg-dark-900 border-t border-gray-200 dark:border-dark-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container-custom py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        onClick: ()=>setIsOpen(false),\n                                        className: \"text-dark-600 dark:text-dark-300 hover:text-primary-400 dark:hover:text-primary-400 transition-colors duration-300 font-medium py-2\",\n                                        children: item.label\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 19\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-4 border-t border-gray-200 dark:border-dark-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://t.me/HeheMinerBot\",\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"btn btn-primary w-full justify-center\",\n                                        onClick: ()=>setIsOpen(false),\n                                        children: \"\\uD83D\\uDE80 Play Now\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/components/Navbar.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Navbar.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nfunction App({ Component, pageProps }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // Add any global initialization here\n        console.log(\"\\uD83C\\uDFAE Hehe Miner Website Loaded!\");\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#33ffff\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"author\",\n                        content: \"Hehe Miner Team\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index, follow\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"language\",\n                        content: \"English\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"revisit-after\",\n                        content: \"7 days\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:site_name\",\n                        content: \"Hehe Miner\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:locale\",\n                        content: \"en_US\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:creator\",\n                        content: \"@Hehe_Miner\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"twitter:site\",\n                        content: \"@Hehe_Miner\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://t.me\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://x.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//youtube.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//telegram.org\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                \"name\": \"Hehe Miner\",\n                                \"description\": \"The ultimate Telegram mining game with real token rewards\",\n                                \"url\": \"https://hehe-miner.vercel.app\",\n                                \"applicationCategory\": \"Game\",\n                                \"operatingSystem\": \"Web, Telegram\",\n                                \"offers\": {\n                                    \"@type\": \"Offer\",\n                                    \"price\": \"0\",\n                                    \"priceCurrency\": \"USD\"\n                                },\n                                \"author\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Hehe Miner Team\",\n                                    \"email\": \"<EMAIL>\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                disableTransitionOnChange: false,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/_app.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/whitepaper.tsx":
/*!**********************************!*\
  !*** ./src/pages/whitepaper.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Whitepaper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"framer-motion\");\n/* harmony import */ var _barrel_optimize_names_FiDownload_FiExternalLink_FiFileText_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiDownload,FiExternalLink,FiFileText!=!react-icons/fi */ \"__barrel_optimize__?names=FiDownload,FiExternalLink,FiFileText!=!./node_modules/react-icons/fi/index.esm.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, framer_motion__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_Layout__WEBPACK_IMPORTED_MODULE_2__, framer_motion__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction Whitepaper() {\n    const sections = [\n        {\n            title: \"Executive Summary\",\n            content: \"Overview of Hehe Miner, tokenomics, and vision for the future of Telegram gaming.\"\n        },\n        {\n            title: \"Game Mechanics\",\n            content: \"Detailed explanation of mining cycles, task system, referral program, and upgrade mechanics.\"\n        },\n        {\n            title: \"Tokenomics\",\n            content: \"Complete breakdown of HEHE token distribution, vesting schedules, and utility.\"\n        },\n        {\n            title: \"Technology Stack\",\n            content: \"Technical architecture, Polygon integration, and smart contract specifications.\"\n        },\n        {\n            title: \"Roadmap\",\n            content: \"Development milestones, TGE timeline, and future expansion plans.\"\n        },\n        {\n            title: \"Team & Advisors\",\n            content: \"Information about the development team and strategic advisors.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Hehe Miner Whitepaper - Technical Documentation | HEHE Token\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Read the complete Hehe Miner whitepaper. Learn about tokenomics, game mechanics, technology stack, and roadmap for the ultimate Telegram mining game.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"keywords\",\n                        content: \"Hehe Miner whitepaper, HEHE token whitepaper, Telegram game documentation, crypto gaming whitepaper, blockchain gaming\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:title\",\n                        content: \"Hehe Miner Whitepaper - Technical Documentation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:description\",\n                        content: \"Complete technical documentation for Hehe Miner, including tokenomics, game mechanics, and roadmap.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        property: \"og:url\",\n                        content: \"https://hehe-miner.vercel.app/whitepaper\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"https://hehe-miner.vercel.app/whitepaper\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"pt-24 pb-16 bg-gradient-to-br from-dark-900 to-dark-800 text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-custom\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center justify-center w-20 h-20 bg-primary-400/20 rounded-2xl mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDownload_FiExternalLink_FiFileText_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiFileText, {\n                                            className: \"w-10 h-10 text-primary-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-6xl font-bold mb-6\",\n                                        children: [\n                                            \"Hehe Miner\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gradient bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent\",\n                                                children: \"Whitepaper\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 mb-8 max-w-3xl mx-auto\",\n                                        children: \"Comprehensive technical documentation covering game mechanics, tokenomics, technology stack, and our vision for the future of Telegram gaming.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn btn-primary btn-large\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDownload_FiExternalLink_FiFileText_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiDownload, {\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Download PDF\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"https://t.me/Hehe_miner_community\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"btn btn-outline btn-large\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiDownload_FiExternalLink_FiFileText_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiExternalLink, {\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Discuss in Community\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"section-padding bg-white dark:bg-dark-900\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-custom\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"mb-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-3xl font-bold text-dark-900 dark:text-white mb-8 text-center\",\n                                                children: \"Document Overview\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-8\",\n                                                children: sections.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.6,\n                                                            delay: index * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"card p-6 hover:shadow-lg transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-dark-900 dark:text-white mb-3\",\n                                                                children: section.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-dark-600 dark:text-dark-300\",\n                                                                children: section.content\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"card p-8 bg-gradient-to-br from-primary-400/10 to-secondary-400/10 border border-primary-400/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-dark-900 dark:text-white mb-6 text-center\",\n                                                children: \"Key Highlights\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-400 rounded-full mt-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                                children: \"500M Total Supply\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 152,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                children: \"Fixed supply with no inflation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 153,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 151,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-400 rounded-full mt-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                                children: \"70% Community Allocation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 159,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                children: \"No vesting for airdrop recipients\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 160,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-primary-400 rounded-full mt-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 164,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                                children: \"Polygon Blockchain\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 166,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                children: \"Low-cost, fast transactions\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 167,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-secondary-400 rounded-full mt-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                                children: \"4-Hour Mining Cycles\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 175,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                children: \"Sustainable reward mechanism\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 176,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-secondary-400 rounded-full mt-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                                children: \"TON Integration\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 182,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                children: \"Speed upgrades with TON payments\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 183,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 181,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-secondary-400 rounded-full mt-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 187,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-semibold text-dark-900 dark:text-white\",\n                                                                                children: \"Referral Rewards\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 189,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-dark-600 dark:text-dark-300\",\n                                                                                children: \"0.5 HEHE per successful referral\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                                lineNumber: 190,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"mt-16 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card p-8 border-2 border-dashed border-primary-400/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-dark-900 dark:text-white mb-4\",\n                                                    children: \"\\uD83D\\uDCC4 Full Whitepaper Coming Soon\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-dark-600 dark:text-dark-300 mb-6\",\n                                                    children: \"We're putting the finishing touches on our comprehensive whitepaper. It will be available for download before our Token Generation Event (TGE).\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://t.me/Hehe_miner_community\",\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"btn btn-primary\",\n                                                            children: \"Get Notified in Community\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://x.com/Hehe_Miner\",\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"btn btn-outline\",\n                                                            children: \"Follow for Updates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/Hehe-coin/hehe-miner/website/src/pages/whitepaper.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/whitepaper.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "framer-motion":
/*!********************************!*\
  !*** external "framer-motion" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = import("framer-motion");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-icons"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fwhitepaper&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fwhitepaper.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();