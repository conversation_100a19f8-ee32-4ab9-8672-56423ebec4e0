const { ethers } = require("hardhat");

async function main() {
  const network = hre.network.name;
  const isTestnet = network === "mumbai";

  console.log(`🚀 Deploying HEHE Token to ${isTestnet ? 'Polygon Mumbai Testnet' : 'Polygon Mainnet'}...\n`);

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.utils.formatEther(await deployer.getBalance()), isTestnet ? "Test MATIC" : "MATIC");
  console.log("");

  // Define allocation wallets (use deployer for testnet)
  const wallets = {
    airdrop: isTestnet ? deployer.address : "******************************************", // Replace with your airdrop wallet
    partners: isTestnet ? deployer.address : "******************************************", // Replace with partners wallet
    team: isTestnet ? deployer.address : "******************************************", // Replace with team wallet
    liquidity: isTestnet ? deployer.address : "******************************************", // Replace with liquidity wallet
    treasury: isTestnet ? deployer.address : "******************************************" // Replace with treasury wallet
  };

  console.log("📋 Allocation Wallets:");
  console.log("Airdrop (70%):", wallets.airdrop);
  console.log("Partners (10%):", wallets.partners);
  console.log("Team (5%):", wallets.team);
  console.log("Liquidity (10%):", wallets.liquidity);
  console.log("Treasury (5%):", wallets.treasury);
  console.log("");

  // Deploy HEHE Token
  console.log("📦 Deploying HEHE Token contract...");
  const HeheToken = await ethers.getContractFactory("HeheToken");
  
  const heheToken = await HeheToken.deploy(
    wallets.airdrop,
    wallets.partners,
    wallets.team,
    wallets.liquidity,
    wallets.treasury
  );

  await heheToken.deployed();

  console.log("✅ HEHE Token deployed to:", heheToken.address);
  const explorerUrl = isTestnet
    ? `https://mumbai.polygonscan.com/address/${heheToken.address}`
    : `https://polygonscan.com/address/${heheToken.address}`;
  console.log("🔗 Explorer:", explorerUrl);
  console.log("");

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const totalSupply = await heheToken.totalSupply();
  const name = await heheToken.name();
  const symbol = await heheToken.symbol();
  const decimals = await heheToken.decimals();
  const tgeExecuted = await heheToken.tgeExecuted();

  console.log("Token Name:", name);
  console.log("Token Symbol:", symbol);
  console.log("Decimals:", decimals);
  console.log("Total Supply:", ethers.utils.formatEther(totalSupply), "HEHE");
  console.log("TGE Executed:", tgeExecuted);
  console.log("");

  // Check allocations (will be 0 until TGE)
  console.log("💰 Current Token Balances (Pre-TGE):");
  const airdropBalance = await heheToken.balanceOf(wallets.airdrop);
  const partnersBalance = await heheToken.balanceOf(wallets.partners);
  const teamBalance = await heheToken.balanceOf(wallets.team);
  const liquidityBalance = await heheToken.balanceOf(wallets.liquidity);
  const treasuryBalance = await heheToken.balanceOf(wallets.treasury);

  console.log("Airdrop Wallet:", ethers.utils.formatEther(airdropBalance), "HEHE (Will be 350M after TGE)");
  console.log("Partners Wallet:", ethers.utils.formatEther(partnersBalance), "HEHE (Will be 50M after TGE)");
  console.log("Team Wallet:", ethers.utils.formatEther(teamBalance), "HEHE (Will be 25M after TGE)");
  console.log("Liquidity Wallet:", ethers.utils.formatEther(liquidityBalance), "HEHE (Will be 50M after TGE)");
  console.log("Treasury Wallet:", ethers.utils.formatEther(treasuryBalance), "HEHE (Will be 25M after TGE)");
  console.log("");

  // Save deployment info
  const deploymentInfo = {
    network: network,
    isTestnet: isTestnet,
    contractAddress: heheToken.address,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    wallets: wallets,
    totalSupply: ethers.utils.formatEther(totalSupply),
    explorerUrl: explorerUrl,
    tgeExecuted: tgeExecuted
  };

  console.log("📄 Deployment Summary:");
  console.log(JSON.stringify(deploymentInfo, null, 2));
  console.log("");

  console.log("🎉 Deployment Complete!");
  console.log("");
  console.log("📋 Next Steps for TGE Process:");
  console.log("1. Verify contract on explorer");
  console.log("2. Take snapshot of game users");
  console.log("3. Set snapshot data with takeSnapshot()");
  console.log("4. Execute TGE with executeTGE()");
  console.log("5. Activate airdrop claims");
  console.log("6. Add liquidity to DEX");
  console.log("7. Integrate with Telegram game");
  console.log("");
  console.log("🔗 Important Links:");
  console.log("Contract:", heheToken.address);
  console.log("Explorer:", explorerUrl);
  console.log("Add to MetaMask:", `${explorerUrl}#code`);
  console.log("");
  console.log("⚠️  Remember: No tokens are minted yet. Execute TGE when ready!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
