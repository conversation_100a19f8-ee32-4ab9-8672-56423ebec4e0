const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying HEHE Token to Polygon Mainnet...\n");

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", ethers.utils.formatEther(await deployer.getBalance()), "MATIC\n");

  // Define allocation wallets
  const wallets = {
    airdrop: "******************************************", // Replace with your airdrop wallet
    partners: "******************************************", // Replace with partners wallet
    team: "******************************************", // Replace with team wallet
    liquidity: "******************************************", // Replace with liquidity wallet
    treasury: "******************************************" // Replace with treasury wallet
  };

  console.log("📋 Allocation Wallets:");
  console.log("Airdrop (70%):", wallets.airdrop);
  console.log("Partners (10%):", wallets.partners);
  console.log("Team (5%):", wallets.team);
  console.log("Liquidity (10%):", wallets.liquidity);
  console.log("Treasury (5%):", wallets.treasury);
  console.log("");

  // Deploy HEHE Token
  console.log("📦 Deploying HEHE Token contract...");
  const HeheToken = await ethers.getContractFactory("HeheToken");
  
  const heheToken = await HeheToken.deploy(
    wallets.airdrop,
    wallets.partners,
    wallets.team,
    wallets.liquidity,
    wallets.treasury
  );

  await heheToken.deployed();

  console.log("✅ HEHE Token deployed to:", heheToken.address);
  console.log("🔗 PolygonScan:", `https://polygonscan.com/address/${heheToken.address}`);
  console.log("");

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const totalSupply = await heheToken.totalSupply();
  const name = await heheToken.name();
  const symbol = await heheToken.symbol();
  const decimals = await heheToken.decimals();

  console.log("Token Name:", name);
  console.log("Token Symbol:", symbol);
  console.log("Decimals:", decimals);
  console.log("Total Supply:", ethers.utils.formatEther(totalSupply), "HEHE");
  console.log("");

  // Check allocations
  console.log("💰 Checking Allocations:");
  const airdropBalance = await heheToken.balanceOf(wallets.airdrop);
  const partnersBalance = await heheToken.balanceOf(wallets.partners);
  const teamBalance = await heheToken.balanceOf(wallets.team);
  const liquidityBalance = await heheToken.balanceOf(wallets.liquidity);
  const treasuryBalance = await heheToken.balanceOf(wallets.treasury);

  console.log("Airdrop Wallet:", ethers.utils.formatEther(airdropBalance), "HEHE (70%)");
  console.log("Partners Wallet:", ethers.utils.formatEther(partnersBalance), "HEHE (10%)");
  console.log("Team Wallet:", ethers.utils.formatEther(teamBalance), "HEHE (5%)");
  console.log("Liquidity Wallet:", ethers.utils.formatEther(liquidityBalance), "HEHE (10%)");
  console.log("Treasury Wallet:", ethers.utils.formatEther(treasuryBalance), "HEHE (5%)");
  console.log("");

  // Save deployment info
  const deploymentInfo = {
    network: "polygon",
    contractAddress: heheToken.address,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    wallets: wallets,
    totalSupply: ethers.utils.formatEther(totalSupply),
    polygonScanUrl: `https://polygonscan.com/address/${heheToken.address}`
  };

  console.log("📄 Deployment Summary:");
  console.log(JSON.stringify(deploymentInfo, null, 2));
  console.log("");

  console.log("🎉 Deployment Complete!");
  console.log("");
  console.log("📋 Next Steps:");
  console.log("1. Verify contract on PolygonScan");
  console.log("2. Add liquidity to DEX (QuickSwap, SushiSwap)");
  console.log("3. Set up airdrop distribution");
  console.log("4. Integrate with Telegram game");
  console.log("5. Apply for exchange listings");
  console.log("");
  console.log("🔗 Important Links:");
  console.log("Contract:", heheToken.address);
  console.log("PolygonScan:", `https://polygonscan.com/address/${heheToken.address}`);
  console.log("Add to MetaMask:", `https://polygonscan.com/token/${heheToken.address}`);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
