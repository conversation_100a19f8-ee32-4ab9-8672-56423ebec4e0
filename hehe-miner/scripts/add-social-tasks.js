const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addSocialTasks() {
  console.log('🎯 Adding new social media tasks...\n')

  // New tasks to add
  const newTasks = [
    {
      title: 'Subscribe to our YouTube Channel',
      description: 'Subscribe to our official YouTube channel for tutorials, updates, and exclusive content about <PERSON>he Miner!',
      reward: 1.0,
      link: 'https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ',
      attachment: 'https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ'
    },
    {
      title: 'Join Hehe Miner Community',
      description: 'Join our official Telegram community channel for the latest news, updates, and connect with other miners!',
      reward: 0.8,
      link: 'https://t.me/Hehe_miner_community',
      attachment: 'https://t.me/Hehe_miner_community'
    }
  ]

  try {
    for (const task of newTasks) {
      // Check if task already exists
      const existingTask = await prisma.task.findFirst({
        where: { 
          OR: [
            { title: task.title },
            { link: task.link }
          ]
        }
      })

      if (existingTask) {
        console.log(`⚠️  Task "${task.title}" already exists, skipping...`)
        continue
      }

      // Create the task
      const createdTask = await prisma.task.create({
        data: {
          title: task.title,
          description: task.description,
          reward: task.reward,
          link: task.link,
          attachment: task.attachment,
          isActive: true
        }
      })

      console.log(`✅ Created task: "${createdTask.title}" (${createdTask.reward} HEHE reward)`)
    }

    // Display all current tasks
    console.log('\n📋 All Current Tasks:')
    const allTasks = await prisma.task.findMany({
      where: { isActive: true },
      orderBy: { reward: 'desc' }
    })

    allTasks.forEach((task, index) => {
      console.log(`${index + 1}. ${task.title} - ${task.reward} HEHE`)
      console.log(`   Link: ${task.link}`)
      console.log(`   Description: ${task.description}`)
      console.log('')
    })

    console.log(`🎉 Successfully processed ${newTasks.length} tasks!`)
    console.log(`📊 Total active tasks: ${allTasks.length}`)

  } catch (error) {
    console.error('❌ Error adding tasks:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
addSocialTasks()
