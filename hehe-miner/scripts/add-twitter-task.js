const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addTwitterTask() {
  console.log('🐦 Adding official Twitter/X task...\n')

  // New Twitter task
  const twitterTask = {
    title: 'Follow us on X (Twitter)',
    description: 'Follow our official X (Twitter) account @<PERSON><PERSON>_Miner for the latest updates, announcements, and community news!',
    reward: 0.6,
    link: 'https://x.com/Hehe_Miner',
    attachment: 'https://x.com/Hehe_Miner'
  }

  try {
    // Check if task already exists with this exact URL
    const existingTask = await prisma.task.findFirst({
      where: { 
        OR: [
          { link: twitterTask.link },
          { link: 'https://twitter.com/Hehe_Miner' },
          { attachment: twitterTask.link }
        ]
      }
    })

    if (existingTask) {
      console.log(`⚠️  Updating existing Twitter task...`)
      
      // Update the existing task with correct URL
      const updatedTask = await prisma.task.update({
        where: { id: existingTask.id },
        data: {
          title: twitterTask.title,
          description: twitterTask.description,
          reward: twitterTask.reward,
          link: twitterTask.link,
          attachment: twitterTask.attachment,
          isActive: true
        }
      })

      console.log(`✅ Updated task: "${updatedTask.title}" with correct X URL`)
    } else {
      // Create new task
      const createdTask = await prisma.task.create({
        data: {
          title: twitterTask.title,
          description: twitterTask.description,
          reward: twitterTask.reward,
          link: twitterTask.link,
          attachment: twitterTask.attachment,
          isActive: true
        }
      })

      console.log(`✅ Created new task: "${createdTask.title}" (${createdTask.reward} HEHE reward)`)
    }

    // Also check for and deactivate old Twitter tasks with incorrect URLs
    const oldTwitterTasks = await prisma.task.findMany({
      where: {
        AND: [
          { isActive: true },
          {
            OR: [
              { link: { contains: 'twitter.com/heheminer' } },
              { link: { contains: 'twitter.com' } },
              { title: { contains: 'Twitter' } }
            ]
          },
          { link: { not: twitterTask.link } }
        ]
      }
    })

    if (oldTwitterTasks.length > 0) {
      console.log(`\n🔄 Found ${oldTwitterTasks.length} old Twitter task(s) to deactivate...`)
      
      for (const oldTask of oldTwitterTasks) {
        await prisma.task.update({
          where: { id: oldTask.id },
          data: { isActive: false }
        })
        console.log(`❌ Deactivated old task: "${oldTask.title}" (${oldTask.link})`)
      }
    }

    // Display all current active tasks
    console.log('\n📋 All Current Active Tasks:')
    const allTasks = await prisma.task.findMany({
      where: { isActive: true },
      orderBy: { reward: 'desc' }
    })

    allTasks.forEach((task, index) => {
      const isNew = task.link === twitterTask.link
      console.log(`${index + 1}. ${task.title} - ${task.reward} HEHE ${isNew ? '⭐ (Updated)' : ''}`)
      console.log(`   Link: ${task.link}`)
      console.log(`   Description: ${task.description}`)
      console.log('')
    })

    console.log(`🎉 Twitter task successfully processed!`)
    console.log(`📊 Total active tasks: ${allTasks.length}`)
    console.log(`🔗 Official X URL: ${twitterTask.link}`)

  } catch (error) {
    console.error('❌ Error processing Twitter task:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the script
addTwitterTask()
