const { ethers } = require("hardhat");

async function main() {
  const network = hre.network.name;
  const isTestnet = network === "mumbai";
  
  console.log(`🚀 Executing TGE on ${isTestnet ? 'Mumbai Testnet' : 'Polygon Mainnet'}...\n`);

  // Contract address (replace with your deployed contract)
  const CONTRACT_ADDRESS = "0x..."; // Replace with actual deployed address
  
  if (CONTRACT_ADDRESS === "0x...") {
    console.error("❌ Please update CONTRACT_ADDRESS in the script");
    process.exit(1);
  }

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Executing with account:", deployer.address);
  console.log("Account balance:", ethers.utils.formatEther(await deployer.getBalance()), isTestnet ? "Test MATIC" : "MATIC");
  console.log("");

  // Get contract instance
  const HeheToken = await ethers.getContractFactory("HeheToken");
  const heheToken = HeheToken.attach(CONTRACT_ADDRESS);

  // Check current state
  console.log("📊 Current Contract State:");
  const tgeExecuted = await heheToken.tgeExecuted();
  const totalSupply = await heheToken.totalSupply();
  const snapshotTimestamp = await heheToken.snapshotTimestamp();
  const totalSnapshotUsers = await heheToken.totalSnapshotUsers();

  console.log("TGE Executed:", tgeExecuted);
  console.log("Total Supply:", ethers.utils.formatEther(totalSupply), "HEHE");
  console.log("Snapshot Timestamp:", snapshotTimestamp.toString());
  console.log("Total Snapshot Users:", totalSnapshotUsers.toString());
  console.log("");

  if (tgeExecuted) {
    console.log("✅ TGE already executed!");
    return;
  }

  if (snapshotTimestamp.eq(0)) {
    console.log("⚠️  No snapshot taken yet. Taking sample snapshot...");
    
    // Sample snapshot data for testing
    const sampleUsers = [
      deployer.address,
      "******************************************",
      "0x8ba1f109551bD432803012645Hac136c30C85bcf"
    ];
    
    const sampleBalances = [
      ethers.utils.parseEther("1000"), // 1000 game points = 1000 HEHE
      ethers.utils.parseEther("500"),  // 500 game points = 500 HEHE
      ethers.utils.parseEther("250")   // 250 game points = 250 HEHE
    ];

    console.log("📸 Taking snapshot...");
    const snapshotTx = await heheToken.takeSnapshot(sampleUsers, sampleBalances);
    await snapshotTx.wait();
    console.log("✅ Snapshot taken!");
    console.log("");
  }

  // Execute TGE
  console.log("🎉 Executing TGE...");
  const tgeTx = await heheToken.executeTGE();
  console.log("Transaction hash:", tgeTx.hash);
  
  console.log("⏳ Waiting for confirmation...");
  const receipt = await tgeTx.wait();
  console.log("✅ TGE executed successfully!");
  console.log("Gas used:", receipt.gasUsed.toString());
  console.log("");

  // Verify TGE execution
  console.log("🔍 Verifying TGE execution...");
  const newTotalSupply = await heheToken.totalSupply();
  const airdropActive = await heheToken.airdropActive();
  const tgeTimestamp = await heheToken.tgeTimestamp();

  console.log("New Total Supply:", ethers.utils.formatEther(newTotalSupply), "HEHE");
  console.log("Airdrop Active:", airdropActive);
  console.log("TGE Timestamp:", new Date(tgeTimestamp.toNumber() * 1000).toISOString());
  console.log("");

  // Check wallet balances
  console.log("💰 Post-TGE Wallet Balances:");
  const airdropWallet = await heheToken.airdropWallet();
  const partnersWallet = await heheToken.partnersWallet();
  const teamWallet = await heheToken.teamWallet();
  const liquidityWallet = await heheToken.liquidityWallet();
  const treasuryWallet = await heheToken.treasuryWallet();

  const airdropBalance = await heheToken.balanceOf(airdropWallet);
  const partnersBalance = await heheToken.balanceOf(partnersWallet);
  const teamBalance = await heheToken.balanceOf(teamWallet);
  const liquidityBalance = await heheToken.balanceOf(liquidityWallet);
  const treasuryBalance = await heheToken.balanceOf(treasuryWallet);

  console.log("Airdrop Wallet:", ethers.utils.formatEther(airdropBalance), "HEHE (70%)");
  console.log("Partners Wallet:", ethers.utils.formatEther(partnersBalance), "HEHE (10%)");
  console.log("Team Wallet:", ethers.utils.formatEther(teamBalance), "HEHE (5%)");
  console.log("Liquidity Wallet:", ethers.utils.formatEther(liquidityBalance), "HEHE (10%)");
  console.log("Treasury Wallet:", ethers.utils.formatEther(treasuryBalance), "HEHE (5%)");
  console.log("");

  // Test airdrop eligibility
  console.log("🎁 Testing Airdrop Eligibility:");
  const [eligible, amount] = await heheToken.isEligibleForAirdrop(deployer.address);
  console.log("Deployer eligible:", eligible);
  console.log("Deployer amount:", ethers.utils.formatEther(amount), "HEHE");
  console.log("");

  console.log("🎉 TGE Complete!");
  console.log("");
  console.log("📋 Next Steps:");
  console.log("1. Users can now claim airdrops");
  console.log("2. Add liquidity to DEX");
  console.log("3. Start trading");
  console.log("4. Integrate with Telegram game");
  console.log("");
  
  const explorerUrl = isTestnet 
    ? `https://mumbai.polygonscan.com/address/${CONTRACT_ADDRESS}`
    : `https://polygonscan.com/address/${CONTRACT_ADDRESS}`;
  console.log("🔗 Contract:", explorerUrl);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ TGE execution failed:", error);
    process.exit(1);
  });
