import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Create sample tasks
  const tasks = [
    {
      title: 'Subscribe to our YouTube Channel',
      description: 'Subscribe to our official YouTube channel for tutorials, updates, and exclusive content about <PERSON><PERSON> Miner!',
      reward: 1.0,
      link: 'https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ',
      attachment: 'https://www.youtube.com/channel/UCEicE93gmTwH-yj9EKO8QHQ'
    },
    {
      title: 'Join Hehe Miner Community',
      description: 'Join our official Telegram community channel for the latest news, updates, and connect with other miners!',
      reward: 0.8,
      link: 'https://t.me/Hehe_miner_community',
      attachment: 'https://t.me/Hehe_miner_community'
    },
    {
      title: 'Follow us on Twitter',
      description: 'Follow our official Twitter account for updates and news',
      reward: 0.5,
      link: 'https://twitter.com/heheminer',
      attachment: 'https://twitter.com/heheminer'
    },
    {
      title: 'Join our Telegram Channel',
      description: 'Join our official Telegram channel for community updates',
      reward: 0.5,
      link: 'https://t.me/heheminer',
      attachment: 'https://t.me/heheminer'
    },
    {
      title: 'Like our Facebook Page',
      description: 'Like and follow our Facebook page for latest news',
      reward: 0.3,
      link: 'https://facebook.com/heheminer',
      attachment: 'https://facebook.com/heheminer'
    },
    {
      title: 'Join Discord Community',
      description: 'Join our Discord server and participate in discussions',
      reward: 0.4,
      link: 'https://discord.gg/heheminer',
      attachment: 'https://discord.gg/heheminer'
    }
  ]

  for (const task of tasks) {
    const existingTask = await prisma.task.findFirst({
      where: { title: task.title }
    })

    if (!existingTask) {
      await prisma.task.create({
        data: task
      })
    }
  }

  console.log('Seed data created successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
