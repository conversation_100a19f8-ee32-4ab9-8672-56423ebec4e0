{"name": "hehe-token-contracts", "version": "1.0.0", "description": "Smart contracts for HEHE Token - Hehe Miner Telegram Game", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:mumbai": "hardhat run scripts/deploy-polygon.js --network mumbai", "deploy:polygon": "hardhat run scripts/deploy-polygon.js --network polygon", "tge:mumbai": "hardhat run scripts/execute-tge.js --network mumbai", "tge:polygon": "hardhat run scripts/execute-tge.js --network polygon", "verify:mumbai": "hardhat verify --network mumbai", "verify:polygon": "hardhat verify --network polygon", "node": "hardhat node", "clean": "hardhat clean"}, "devDependencies": {"@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-etherscan": "^3.1.7", "@nomiclabs/hardhat-waffle": "^2.0.6", "@openzeppelin/contracts": "^4.9.3", "chai": "^4.3.8", "dotenv": "^16.3.1", "ethereum-waffle": "^4.0.10", "ethers": "^5.7.2", "hardhat": "^2.17.1"}, "keywords": ["ethereum", "polygon", "smart-contracts", "erc20", "token", "hehe", "telegram", "gaming"], "author": "tfarooqui664", "license": "MIT"}